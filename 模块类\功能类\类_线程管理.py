# -*- coding: utf-8 -*-
"""
线程管理
使用Python原生threading模块
"""

import threading
import time
import logging
from .类_其它功能 import 类_其它功能
from .类_核心计算 import 类_核心计算
# 获取当前模块的日志记录器
logger = logging.getLogger(__name__)


def 多线程主程序(机器人编号: str, 交易对: str, 停止标志: threading.Event, UI更新器=None) -> None:
    """
    最简单的交易死循环函数

    @param 机器人编号 (str): 机器人的唯一编号
    @param 交易对 (str): 交易对名称，如 "BTC-USDT"
    @param 停止标志 (threading.Event): threading.Event对象，用于控制循环停止
    @param UI更新器: 线程安全UI更新器实例，用于跨线程更新UI
    @return None
    @exception Exception: 循环执行过程中可能发生的未知错误
    """
    # 步骤1: 记录启动日志
    # 记录交易循环开始执行的信息，包含机器人编号和交易对
    print(f"开始执行交易循环: {机器人编号} - {交易对}")
    logger.info(f"开始执行交易循环: {机器人编号} - {交易对}")
    
    # 步骤1.5: 获取系统配置
    # 获取当前使用的交易所和下单模式
    try:
        其它功能 = 类_其它功能()
        核心计算 = 类_核心计算(参_UI更新器=UI更新器)
        交易所, 下单模式 = 其它功能.获取交易所和下单模式()
        print(f"[{机器人编号}] 当前使用交易所: {交易所}, 下单模式: {下单模式}")
        logger.info(f"[{机器人编号}] 当前使用交易所: {交易所}, 下单模式: {下单模式}")
    except Exception as e:
        print(f"[{机器人编号}] 获取系统配置出错: {e}")
        logger.error(f"[{机器人编号}] 获取系统配置出错: {e}")
        交易所 = "币安"  # 默认使用币安
        下单模式 = "测试模式"  # 默认使用测试模式
        核心计算 = 类_核心计算(参_UI更新器=UI更新器)  # 确保核心计算对象被创建
    
    循环次数 = 0
    
    # 步骤2: 执行交易循环
    # 死循环，直到收到停止信号
    # 每次循环都会检查停止标志，如果设置了则退出循环
    while not 停止标志.is_set():
        try:
            循环次数 += 1
            核心计算返回值 = 核心计算.处理逻辑主函数(机器人编号, 交易对, 交易所, 下单模式)
            print(f"核心计算返回值: {核心计算返回值}")
            print(f"[{机器人编号}] 第{循环次数}次循环 - 交易对: {交易对} - 交易所: {交易所} - 下单模式: {下单模式}")
      
            
            # 步骤2.5: 等待下次循环
            # 每5秒执行一次，避免过于频繁的请求
            time.sleep(1)
            
        except Exception as e:
            # 步骤2.6: 异常处理
            # 如果循环过程中出现异常，记录错误日志
            # 出错后稍微等待一下再继续，避免异常导致程序崩溃
            print(f"[{机器人编号}] 循环出错: {e}")
            logger.error(f"[{机器人编号}] 循环出错: {e}")
            # 出错后稍微等待一下再继续
            time.sleep(1)
    
    # 步骤3: 记录结束日志
    # 记录交易循环结束的信息
    print(f"交易循环结束: {机器人编号} - {交易对}")
    logger.info(f"交易循环结束: {机器人编号} - {交易对}")


def 后台停止监控(机器人编号: str, 线程对象: threading.Thread, 停止标志: threading.Event, 线程字典: dict) -> None:
    """
    后台监控线程停止的函数
    
    @param 机器人编号 (str): 机器人编号
    @param 线程对象 (threading.Thread): 要监控的线程对象
    @param 停止标志 (threading.Event): 停止标志
    @param 线程字典 (dict): 线程字典引用
    @return None
    @exception Exception: 监控过程中可能发生的未知错误
    """
    try:
        # 步骤1: 等待线程结束
        # 等待线程结束，最多等待10秒
        # 如果10秒内线程没有结束，则认为停止超时
        线程对象.join(timeout=10)
        
        # 步骤2: 检查线程状态
        # 检查线程是否还在运行
        # 如果还在运行，记录超时警告；如果已停止，记录成功信息
        if 线程对象.is_alive():
            print(f"机器人 {机器人编号} 的线程停止超时")
            logger.warning(f"机器人 {机器人编号} 的线程停止超时")
        else:
            print(f"成功停止机器人 {机器人编号} 的交易线程")
            logger.info(f"成功停止机器人 {机器人编号} 的交易线程")
        
        # 步骤3: 清理线程字典
        # 从字典中移除该机器人的线程信息
        # 使用锁保护，避免并发访问导致的问题
        with threading.Lock():
            if 机器人编号 in 线程字典:
                del 线程字典[机器人编号]
                
    except Exception as e:
        # 步骤4: 异常处理
        # 如果监控过程中出现异常，记录错误日志
        print(f"后台停止监控出错: {e}")
        logger.error(f"后台停止监控出错: {e}")


class 类_多线程管理器:
    """
    最简单的线程管理器
    只负责启动和停止线程
    """
    
    def __init__(self, 参_UI更新器=None):
        """
        初始化线程管理器

        @param 参_UI更新器: 线程安全UI更新器实例
        @return None
        @exception Exception: 初始化过程中可能发生的未知错误
        """
        # 步骤1: 初始化线程字典
        # 存储线程的字典 {机器人编号: (线程对象, 停止标志)}
        # 用于管理所有正在运行的交易线程
        self.线程字典 = {}

        # 步骤2: 初始化线程锁
        # 添加线程锁，保护字典操作
        # 避免多线程同时访问字典导致的数据竞争问题
        self.线程锁 = threading.Lock()

        # 步骤3: 保存UI更新器引用
        # 保存UI更新器的引用，用于传递给工作线程
        self.UI更新器 = 参_UI更新器

        # 步骤4: 记录初始化完成
        # 记录线程管理器初始化完成的信息
        print("简单线程管理器初始化完成")
        logger.info("简单线程管理器初始化完成")
    
    def 启动线程(self, 机器人编号: str, 交易对: str) -> bool:
        """
        启动一个新的交易线程
        
        @param 机器人编号 (str): 机器人的唯一编号
        @param 交易对 (str): 交易对名称
            
        @return bool: 是否启动成功
        @exception Exception: 启动过程中可能发生的未知错误
        """
        try:
            # 步骤1: 检查线程状态
            # 使用锁保护字典访问，避免并发问题
            with self.线程锁:
                # 检查线程是否已经存在
                if 机器人编号 in self.线程字典:
                    线程对象, 停止标志 = self.线程字典[机器人编号]
                    if 线程对象.is_alive():
                        print(f"机器人 {机器人编号} 已经在运行中")
                        return False
                    else:
                        # 清理已结束的线程
                        del self.线程字典[机器人编号]
                
                # 步骤2: 创建停止标志
                # 创建threading.Event对象，用于控制线程停止
                停止标志 = threading.Event()
                
                # 步骤3: 创建新线程
                # 创建新的交易线程，传入必要的参数，包括UI更新器
                线程对象 = threading.Thread(
                    target=多线程主程序,
                    args=(机器人编号, 交易对, 停止标志, self.UI更新器),
                    name=f"交易线程-{机器人编号}"
                )
                
                # 步骤4: 设置线程属性
                # 设置为守护线程，主程序退出时自动结束
                线程对象.daemon = True
                
                # 步骤5: 启动线程
                # 启动新创建的线程
                线程对象.start()
                
                # 步骤6: 保存到字典
                # 将线程对象和停止标志保存到字典中
                self.线程字典[机器人编号] = (线程对象, 停止标志)
            
            # 步骤7: 记录启动成功
            # 记录线程启动成功的信息
            print(f"成功启动机器人 {机器人编号} 的交易线程")
            logger.info(f"成功启动机器人 {机器人编号} 的交易线程")
            return True
            
        except Exception as e:
            # 步骤8: 异常处理
            # 如果启动过程中出现异常，记录错误日志并返回失败
            print(f"启动线程失败: {e}")
            logger.error(f"启动线程失败: {e}")
            return False
    
    def 停止线程(self, 机器人编号: str) -> bool:
        """
        停止指定的交易线程（非阻塞方式）
        
        @param 机器人编号 (str): 要停止的机器人编号
            
        @return bool: 是否停止成功
        @exception Exception: 停止过程中可能发生的未知错误
        """
        try:
            # 步骤1: 检查线程是否存在
            # 使用锁保护字典访问，避免并发问题
            with self.线程锁:
                if 机器人编号 not in self.线程字典:
                    print(f"机器人 {机器人编号} 的线程不存在")
                    return False
                
                线程对象, 停止标志 = self.线程字典[机器人编号]
                
                # 步骤2: 检查线程状态
                # 如果线程已经停止，直接清理并返回成功
                if not 线程对象.is_alive():
                    print(f"机器人 {机器人编号} 的线程已经停止")
                    del self.线程字典[机器人编号]
                    return True
                
                # 步骤3: 设置停止标志
                # 设置停止标志，通知线程停止运行
                停止标志.set()
                
                # 步骤4: 创建监控线程
                # 创建后台监控线程，避免阻塞主线程
                # 监控线程会等待原线程结束并清理字典
                监控线程 = threading.Thread(
                    target=后台停止监控,
                    args=(机器人编号, 线程对象, 停止标志, self.线程字典),
                    name=f"停止监控-{机器人编号}"
                )
                监控线程.daemon = True
                监控线程.start()
            
            # 步骤5: 记录停止信号发送
            # 记录已发送停止信号的信息
            print(f"已发送停止信号给机器人 {机器人编号}，正在后台停止...")
            logger.info(f"已发送停止信号给机器人 {机器人编号}，正在后台停止...")
            return True
            
        except Exception as e:
            # 步骤6: 异常处理
            # 如果停止过程中出现异常，记录错误日志并返回失败
            print(f"停止线程失败: {e}")
            logger.error(f"停止线程失败: {e}")
            return False
    
    def 停止所有线程(self) -> None:
        """
        停止所有交易线程

        @return None
        @exception Exception: 停止过程中可能发生的未知错误
        """
        # 步骤1: 获取所有机器人编号
        # 使用锁保护字典访问，获取所有正在运行的机器人编号
        with self.线程锁:
            机器人编号列表 = list(self.线程字典.keys())
        
        # 步骤2: 逐个停止线程
        # 遍历所有机器人编号，逐个调用停止线程方法
        for 机器人编号 in 机器人编号列表:
            self.停止线程(机器人编号)
        
        # 步骤3: 记录操作完成
        # 记录已发送停止信号给所有线程的信息
        print("已发送停止信号给所有线程")
        logger.info("已发送停止信号给所有线程")
    
    def 获取线程状态(self, 机器人编号: str) -> str:
        """
        获取指定机器人的线程状态
        
        @param 机器人编号 (str): 机器人编号
            
        @return str: 状态字符串
        @exception Exception: 获取状态过程中可能发生的未知错误
        """
        # 步骤1: 检查线程是否存在
        # 使用锁保护字典访问，检查机器人编号是否在字典中
        with self.线程锁:
            if 机器人编号 not in self.线程字典:
                return "未启动"
            
            线程对象, 停止标志 = self.线程字典[机器人编号]
            
            # 步骤2: 返回线程状态
            # 根据线程是否存活返回相应的状态
            if 线程对象.is_alive():
                return "运行中"
            else:
                return "已停止"
    
    def 获取所有线程状态(self) -> dict:
        """
        获取所有线程的状态
        
        @return dict: 状态字典 {机器人编号: 状态}
        @exception Exception: 获取状态过程中可能发生的未知错误
        """
        # 步骤1: 初始化状态字典
        # 创建空字典用于存储所有线程的状态
        状态字典 = {}
        
        # 步骤2: 遍历所有线程
        # 使用锁保护字典访问，遍历所有线程并获取状态
        with self.线程锁:
            for 机器人编号, (线程对象, 停止标志) in self.线程字典.items():
                # 步骤3: 判断线程状态
                # 根据线程是否存活设置相应的状态
                if 线程对象.is_alive():
                    状态字典[机器人编号] = "运行中"
                else:
                    状态字典[机器人编号] = "已停止"
        
        # 步骤4: 返回状态字典
        # 返回包含所有线程状态的字典
        return 状态字典


# 测试代码
if __name__ == "__main__":
    # 创建线程管理器
    管理器 = 类_多线程管理器()
    
    # 启动一个测试线程
    管理器.启动线程("TEST001", "BTC-USDT")
    
    # 等待5秒
    time.sleep(5)
    
    # 停止线程
    管理器.停止线程("TEST001")
    
    print("测试完成")
