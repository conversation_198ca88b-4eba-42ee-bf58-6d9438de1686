"""
加密工具类
使用AES加密算法处理敏感数据的加密和解密
"""

import base64
import os
from Crypto.Cipher import AES
from Crypto.Random import get_random_bytes
from Crypto.Protocol.KDF import PBKDF2
from Crypto.Util.Padding import pad, unpad
import logging

# 获取当前模块的日志记录器
logger = logging.getLogger(__name__)

class 类_加密工具:
    """
    加密工具类
    使用AES-256-CBC模式进行数据加密和解密
    """
    
    def __init__(self):
        """
        初始化加密工具
        生成或加载加密密钥
        """
        self.密钥文件路径 = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "配置", "加密密钥.key")
        self.盐值文件路径 = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "配置", "盐值.key")
        self._确保配置目录存在()
        self.密钥 = self._初始化密钥()
        logger.info("加密工具类初始化完成")
    
    def _确保配置目录存在(self):
        """确保配置目录存在"""
        配置目录 = os.path.dirname(self.密钥文件路径)
        if not os.path.exists(配置目录):
            os.makedirs(配置目录)
            logger.info(f"创建配置目录: {配置目录}")
    
    def _初始化密钥(self):
        """
        初始化AES密钥
        如果密钥不存在则生成新密钥
        """
        try:
            if not os.path.exists(self.密钥文件路径) or not os.path.exists(self.盐值文件路径):
                return self._生成新密钥()
            
            with open(self.密钥文件路径, 'rb') as f:
                return f.read()
            
        except Exception as e:
            logger.error(f"初始化密钥失败: {e}", exc_info=True)
            return self._生成新密钥()
    
    def _生成新密钥(self):
        """
        生成新的AES密钥并保存
        """
        try:
            # 生成随机盐值
            盐值 = get_random_bytes(32)
            with open(self.盐值文件路径, 'wb') as f:
                f.write(盐值)
            
            # 使用PBKDF2生成密钥
            密钥 = PBKDF2(
                password='Quantification',
                salt=盐值,
                dkLen=32,  # AES-256需要32字节密钥
                count=100000  # 迭代次数
            )
            
            # 保存密钥
            with open(self.密钥文件路径, 'wb') as f:
                f.write(密钥)
            
            logger.info("生成新的AES密钥")
            return 密钥
            
        except Exception as e:
            logger.error(f"生成新密钥失败: {e}", exc_info=True)
            raise
    
    def 加密(self, 参_明文):
        """
        使用AES-256-CBC模式加密数据
        
        参数:
            参_明文: 要加密的字符串
            
        返回:
            str: 加密后的字符串（base64编码）
        """
        try:
            if not 参_明文:
                return ""
                
            # 生成随机IV
            iv = get_random_bytes(AES.block_size)
            
            # 创建AES加密器
            加密器 = AES.new(self.密钥, AES.MODE_CBC, iv)
            
            # 对数据进行填充并加密
            字节数据 = 参_明文.encode()
            填充数据 = pad(字节数据, AES.block_size)
            加密数据 = 加密器.encrypt(填充数据)
            
            # 将IV和加密数据组合并进行base64编码
            结果 = base64.b64encode(iv + 加密数据).decode('utf-8')
            return 结果
            
        except Exception as e:
            logger.error(f"加密数据失败: {e}", exc_info=True)
            return ""
    
    def 解密(self, 参_密文):
        """
        解密AES加密的数据
        
        参数:
            参_密文: 加密后的字符串（base64编码）
            
        返回:
            str: 解密后的原始字符串
        """
        try:
            if not 参_密文:
                return ""
                
            # base64解码
            原始数据 = base64.b64decode(参_密文.encode('utf-8'))
            
            # 提取IV和加密数据
            iv = 原始数据[:AES.block_size]
            加密数据 = 原始数据[AES.block_size:]
            
            # 创建AES解密器
            解密器 = AES.new(self.密钥, AES.MODE_CBC, iv)
            
            # 解密并去除填充
            解密数据 = 解密器.decrypt(加密数据)
            原始字节 = unpad(解密数据, AES.block_size)
            
            return 原始字节.decode('utf-8')
            
        except Exception as e:
            logger.error(f"解密数据失败: {e}", exc_info=True)
            return "" 