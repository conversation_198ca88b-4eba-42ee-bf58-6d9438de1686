"""
开平仓日志数据访问类
负责处理开平仓日志的数据库操作
"""

from typing import List, Dict, Any, Optional
import logging
from .类_数据库连接管理 import 类_数据库连接管理

# 获取当前模块的日志记录器
logger = logging.getLogger(__name__)

class 类_开平仓日志数据访问:
    """
    开平仓日志数据访问类
    负责处理开平仓日志的数据库操作
    """
    
    def __init__(self, 参_数据库连接管理: Optional[类_数据库连接管理] = None):
        """
        初始化开平仓日志数据访问类
        
        参数:
            参_数据库连接管理: 数据库连接管理实例
        """
        self.类_数据库连接管理 = 参_数据库连接管理 or 类_数据库连接管理()
        logger.info("开平仓日志数据访问类初始化完成")
    
    def 添加开平仓日志(self, 参_日志数据: Dict[str, Any]) -> bool:
        """
        添加开平仓日志
        
        参数:
            参_日志数据: 日志数据字典
            
        返回:
            操作是否成功
        """
        连接 = self.类_数据库连接管理.获取连接()
        游标 = 连接.cursor()
        
        try:
            插入SQL = """
            INSERT INTO 开平仓日志表 
            (交易所, 交易对, 订单类型, 订单价格, 订单数量, 订单时间) 
            VALUES (?, ?, ?, ?, ?, ?)
            """
            
            游标.execute(插入SQL, (
                参_日志数据.get('交易所'),
                参_日志数据.get('交易对'),
                参_日志数据.get('订单类型'),
                参_日志数据.get('订单价格'),
                参_日志数据.get('订单数量'),
                参_日志数据.get('订单时间')
            ))
            
            连接.commit()
            logger.info(f"添加开平仓日志成功: {参_日志数据.get('订单类型', '未知')}")
            return True
            
        except Exception as e:
            连接.rollback()
            logger.error(f"添加开平仓日志失败: {e}", exc_info=True)
            return False
    
    def 获取开平仓日志列表(self) -> List[Dict[str, Any]]:
        """
        获取开平仓日志列表
        
        返回:
            日志数据列表
        """
        连接 = self.类_数据库连接管理.获取连接()
        游标 = 连接.cursor()
        
        try:
            查询SQL = "SELECT * FROM 开平仓日志表 ORDER BY 订单时间 DESC"
            游标.execute(查询SQL)
            结果 = 游标.fetchall()
            结果列表 = []
            
            for 行 in 结果:
                日志数据 = {
                    '序号': 行['序号'],
                    '交易所': 行['交易所'],
                    '交易对': 行['交易对'],
                    '订单类型': 行['订单类型'],
                    '订单价格': 行['订单价格'],
                    '订单数量': 行['订单数量'],
                    '订单时间': 行['订单时间'],
                    '创建时间': 行['创建时间']
                }
                结果列表.append(日志数据)
            
            logger.debug(f"获取开平仓日志列表成功，共 {len(结果列表)} 条记录")
            return 结果列表
            
        except Exception as e:
            logger.error(f"获取开平仓日志列表失败: {e}", exc_info=True)
            return [] 