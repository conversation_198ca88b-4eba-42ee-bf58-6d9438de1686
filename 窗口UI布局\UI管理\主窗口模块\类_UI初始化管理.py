# -*- coding: utf-8 -*-
"""
UI初始化管理模块
负责主窗口的UI初始化和基础设置
"""

from PySide6.QtWidgets import QMainWindow, QApplication
from PySide6.QtCore import Qt
from 窗口UI布局.UI文件.Main_window_ui import Ui_MainWindow
import logging
from 数据库.数据访问.类_表格列映射管理 import 类_中控表格列, 类_持仓表格列, 类_平仓表格列, 类_历史数据表格列, 类_日志表格列

# 获取当前模块的日志记录器
logger = logging.getLogger(__name__)

class 类_UI初始化管理:
    """
    UI初始化管理类
    负责处理主窗口的UI初始化和基础设置
    """
    
    def __init__(self, 参_主窗口):
        """
        初始化UI管理器
        
        参数:
            参_主窗口: 主窗口实例
        """
        self.主窗口 = 参_主窗口
        logger.info("UI初始化管理类初始化完成")
    
    def 初始化界面(self):
        """
        初始化界面元素
        设置表格列名、默认值等
        """
        self.初始化策略控制表格()
        self.初始化持仓表格()
        self.初始化平仓表格()
        self.初始化历史订单表格()
        self.初始化日志表格()
        logger.info("界面初始化完成")
    
    def 初始化策略控制表格(self):
        """
        初始化策略控制表格
        """
        # 使用映射类获取列说明
        中控表格列说明 = 类_中控表格列.获取列说明()
        局_控制表格列名 = list(中控表格列说明.values())
        
        self.主窗口.tableWidget_control.setColumnCount(len(局_控制表格列名))
        self.主窗口.tableWidget_control.setHorizontalHeaderLabels(局_控制表格列名)
        self.主窗口.tableWidget_control.verticalHeader().setVisible(False)
        self.主窗口.tableWidget_control.horizontalHeader().setDefaultAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 设置列宽
        列宽配置 = {
            类_中控表格列.列_序号: 60,   # 序号
            类_中控表格列.列_交易对: 100,  # 交易对
            类_中控表格列.列_价格: 100,  # 价格
            类_中控表格列.列_预配本金: 100,  # 预配本金
            # ... 其他列宽配置
        }
        for 列, 宽度 in 列宽配置.items():
            self.主窗口.tableWidget_control.setColumnWidth(列, 宽度)
        
        logger.debug("策略控制表格初始化完成")
    
    def 初始化持仓表格(self):
        """
        初始化持仓表格
        """
        # 使用映射类获取列说明
        持仓表格列说明 = 类_持仓表格列.获取列说明()
        局_持仓表格列名 = list(持仓表格列说明.values())
        
        self.主窗口.tableWidget_Open_Orders.setColumnCount(len(局_持仓表格列名))
        self.主窗口.tableWidget_Open_Orders.setHorizontalHeaderLabels(局_持仓表格列名)
        self.主窗口.tableWidget_Open_Orders.verticalHeader().setVisible(False)
        self.主窗口.tableWidget_Open_Orders.horizontalHeader().setDefaultAlignment(Qt.AlignmentFlag.AlignCenter)
        
        logger.debug("持仓表格初始化完成")
    
    def 初始化平仓表格(self):
        """
        初始化平仓表格
        """
        # 使用映射类获取列说明
        平仓表格列说明 = 类_平仓表格列.获取列说明()
        局_平仓表格列名 = list(平仓表格列说明.values())
        
        self.主窗口.tableWidget_Closing_Orders.setColumnCount(len(局_平仓表格列名))
        self.主窗口.tableWidget_Closing_Orders.setHorizontalHeaderLabels(局_平仓表格列名)
        self.主窗口.tableWidget_Closing_Orders.verticalHeader().setVisible(False)
        self.主窗口.tableWidget_Closing_Orders.horizontalHeader().setDefaultAlignment(Qt.AlignmentFlag.AlignCenter)
        
        logger.debug("平仓表格初始化完成")
    
    def 初始化历史订单表格(self):
        """
        初始化历史订单表格
        """
        # 使用映射类获取列说明
        历史表格列说明 = 类_历史数据表格列.获取列说明()
        局_历史表格列名 = list(历史表格列说明.values())
        
        self.主窗口.tableWidget_Historical_orders.setColumnCount(len(局_历史表格列名))
        self.主窗口.tableWidget_Historical_orders.setHorizontalHeaderLabels(局_历史表格列名)
        self.主窗口.tableWidget_Historical_orders.verticalHeader().setVisible(False)
        self.主窗口.tableWidget_Historical_orders.horizontalHeader().setDefaultAlignment(Qt.AlignmentFlag.AlignCenter)
        
        logger.debug("历史订单表格初始化完成")
    
    def 初始化日志表格(self):
        """
        初始化日志表格
        """
        # 使用映射类获取列说明
        日志表格列说明 = 类_日志表格列.获取列说明()
        局_日志表格列名 = list(日志表格列说明.values())
        
        self.主窗口.tableWidget_OpenAndClose_log.setColumnCount(len(局_日志表格列名))
        self.主窗口.tableWidget_OpenAndClose_log.setHorizontalHeaderLabels(局_日志表格列名)
        self.主窗口.tableWidget_OpenAndClose_log.verticalHeader().setVisible(False)
        self.主窗口.tableWidget_OpenAndClose_log.horizontalHeader().setDefaultAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 设置日志表格列宽
        列宽配置 = {
            类_日志表格列.列_序号: 60,   # 序号
            类_日志表格列.列_交易对: 100,  # 交易对
            类_日志表格列.列_机器人编号: 100,  # 机器人编号
            类_日志表格列.列_订单类型: 80,   # 订单类型
            类_日志表格列.列_蜡烛时间: 120,  # 蜡烛时间
            类_日志表格列.列_开平仓时间: 120,  # 开平仓时间
            类_日志表格列.列_金额: 100,  # 金额
            类_日志表格列.列_数量: 100,  # 数量
            类_日志表格列.列_手续费: 100,  # 手续费
            类_日志表格列.列_收益收益率: 120,  # 收益/收益率
            类_日志表格列.列_策略配置: 100, # 策略配置
            类_日志表格列.列_策略类型: 100, # 策略类型
            类_日志表格列.列_子策略: 100  # 子策略
        }
        for 列, 宽度 in 列宽配置.items():
            self.主窗口.tableWidget_OpenAndClose_log.setColumnWidth(列, 宽度)
        
        logger.debug("日志表格初始化完成")
    
    def 设置窗口属性(self):
        """
        设置窗口的基本属性
        """
        self.主窗口.setWindowTitle("现货量化软件 - 主控制台")
        self.居中显示窗口()
        logger.info("窗口属性设置完成")
    
    def 居中显示窗口(self):
        """
        让窗口在屏幕中央显示
        """
        局_屏幕 = QApplication.primaryScreen().geometry()
        局_窗口尺寸 = self.主窗口.geometry()
        
        局_x = (局_屏幕.width() - 局_窗口尺寸.width()) // 2
        局_y = (局_屏幕.height() - 局_窗口尺寸.height()) // 2
        
        self.主窗口.move(局_x, 局_y)
        logger.debug("窗口已居中显示") 