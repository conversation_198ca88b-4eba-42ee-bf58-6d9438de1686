# -*- coding: utf-8 -*-
"""
跨线程UI更新功能测试
验证线程安全UI更新器的正确性和稳定性
"""

import sys
import os
import time
import threading
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
from PySide6.QtCore import QTimer

# 添加项目根目录到sys.path
当前目录 = os.path.dirname(os.path.abspath(__file__))
项目根目录 = os.path.dirname(当前目录)
if 项目根目录 not in sys.path:
    sys.path.insert(0, 项目根目录)

# 导入需要测试的模块
try:
    from 窗口UI布局.UI管理.主窗口模块.类_线程安全UI更新器 import 类_线程安全UI更新器
    from 模块类.功能类.类_线程管理 import 类_多线程管理器
    print("成功导入测试模块")
except ImportError as e:
    print(f"导入模块失败: {e}")
    sys.exit(1)

class 测试主窗口(QMainWindow):
    """
    简单的测试主窗口
    用于测试跨线程UI更新功能
    """
    
    def __init__(self):
        super().__init__()
        self.初始化UI()
        self.初始化测试组件()
    
    def 初始化UI(self):
        """初始化UI界面"""
        self.setWindowTitle("跨线程UI更新测试")
        self.setGeometry(100, 100, 600, 400)
        
        # 创建中央部件
        中央部件 = QWidget()
        self.setCentralWidget(中央部件)
        
        # 创建布局
        布局 = QVBoxLayout(中央部件)
        
        # 创建状态显示标签
        self.状态标签 = QLabel("测试状态: 准备就绪")
        布局.addWidget(self.状态标签)
        
        # 创建数据显示标签
        self.数据标签1 = QLabel("机器人BOT001 - 本次下单: 0")
        self.数据标签2 = QLabel("机器人BOT001 - 累计下单: 0")
        self.数据标签3 = QLabel("机器人BOT001 - 订单数量: 0")
        布局.addWidget(self.数据标签1)
        布局.addWidget(self.数据标签2)
        布局.addWidget(self.数据标签3)
        
        # 创建测试按钮
        self.开始测试按钮 = QPushButton("开始跨线程UI更新测试")
        self.开始测试按钮.clicked.connect(self.开始测试)
        布局.addWidget(self.开始测试按钮)
        
        self.停止测试按钮 = QPushButton("停止测试")
        self.停止测试按钮.clicked.connect(self.停止测试)
        self.停止测试按钮.setEnabled(False)
        布局.addWidget(self.停止测试按钮)
    
    def 初始化测试组件(self):
        """初始化测试组件"""
        # 创建线程安全UI更新器
        self.线程安全UI更新器 = 类_线程安全UI更新器(self)
        
        # 连接UI更新信号到测试槽函数
        self.线程安全UI更新器.信号_更新单个单元格.connect(self.测试槽_更新单个数据)
        self.线程安全UI更新器.信号_更新多个单元格.connect(self.测试槽_更新多个数据)
        self.线程安全UI更新器.信号_显示状态消息.connect(self.测试槽_显示状态)
        
        # 测试线程控制
        self.测试线程 = None
        self.停止标志 = threading.Event()
        
        print("测试组件初始化完成")
    
    def 测试槽_更新单个数据(self, 机器人编号, 字段名, 新值):
        """测试用的单个数据更新槽函数"""
        if 字段名 == "本次下单":
            self.数据标签1.setText(f"机器人{机器人编号} - 本次下单: {新值}")
        elif 字段名 == "累计下单":
            self.数据标签2.setText(f"机器人{机器人编号} - 累计下单: {新值}")
        elif 字段名 == "订单数量":
            self.数据标签3.setText(f"机器人{机器人编号} - 订单数量: {新值}")
        print(f"UI更新: {机器人编号} - {字段名} = {新值}")
    
    def 测试槽_更新多个数据(self, 机器人编号, 字段字典):
        """测试用的多个数据更新槽函数"""
        for 字段名, 新值 in 字段字典.items():
            self.测试槽_更新单个数据(机器人编号, 字段名, 新值)
        print(f"批量UI更新: {机器人编号} - {len(字段字典)}个字段")
    
    def 测试槽_显示状态(self, 消息, 超时时间):
        """测试用的状态显示槽函数"""
        self.状态标签.setText(f"测试状态: {消息}")
        print(f"状态更新: {消息}")
        
        # 设置定时器清除状态
        QTimer.singleShot(超时时间, lambda: self.状态标签.setText("测试状态: 就绪"))
    
    def 开始测试(self):
        """开始跨线程UI更新测试"""
        print("开始跨线程UI更新测试")
        
        # 重置停止标志
        self.停止标志.clear()
        
        # 创建测试线程
        self.测试线程 = threading.Thread(target=self.测试线程函数, daemon=True)
        self.测试线程.start()
        
        # 更新按钮状态
        self.开始测试按钮.setEnabled(False)
        self.停止测试按钮.setEnabled(True)
        
        # 显示开始状态
        self.线程安全UI更新器.请求显示状态消息("测试开始", 3000)
    
    def 停止测试(self):
        """停止测试"""
        print("停止跨线程UI更新测试")
        
        # 设置停止标志
        self.停止标志.set()
        
        # 更新按钮状态
        self.开始测试按钮.setEnabled(True)
        self.停止测试按钮.setEnabled(False)
        
        # 显示停止状态
        self.线程安全UI更新器.请求显示状态消息("测试停止", 3000)
    
    def 测试线程函数(self):
        """测试线程的主函数"""
        print("测试线程开始运行")
        
        循环次数 = 0
        
        while not self.停止标志.is_set():
            try:
                循环次数 += 1
                
                # 模拟交易数据更新
                本次下单 = 100 + (循环次数 * 10)
                累计下单 = 本次下单 * 循环次数
                订单数量 = 循环次数
                
                # 测试单个字段更新
                if 循环次数 % 3 == 1:
                    self.线程安全UI更新器.请求更新单个单元格("BOT001", "本次下单", 本次下单)
                    time.sleep(0.5)
                    self.线程安全UI更新器.请求更新单个单元格("BOT001", "累计下单", 累计下单)
                    time.sleep(0.5)
                    self.线程安全UI更新器.请求更新单个单元格("BOT001", "订单数量", 订单数量)
                
                # 测试批量字段更新
                elif 循环次数 % 3 == 2:
                    更新字段 = {
                        "本次下单": 本次下单,
                        "累计下单": 累计下单,
                        "订单数量": 订单数量
                    }
                    self.线程安全UI更新器.请求更新多个单元格("BOT001", 更新字段)
                
                # 测试状态消息更新
                else:
                    状态消息 = f"第{循环次数}次更新完成"
                    self.线程安全UI更新器.请求显示状态消息(状态消息, 2000)
                
                print(f"测试线程第{循环次数}次循环完成")
                
                # 等待2秒再进行下次更新
                time.sleep(2)
                
            except Exception as e:
                print(f"测试线程出错: {e}")
                break
        
        print("测试线程结束运行")

def 运行测试():
    """运行跨线程UI更新测试"""
    print("=== 开始跨线程UI更新测试 ===")
    
    # 创建应用
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    测试窗口 = 测试主窗口()
    测试窗口.show()
    
    print("测试窗口已显示，请点击按钮开始测试")
    
    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    运行测试()
