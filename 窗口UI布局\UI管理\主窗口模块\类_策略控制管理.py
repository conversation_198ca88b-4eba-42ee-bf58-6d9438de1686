# -*- coding: utf-8 -*-
"""
策略控制管理模块
负责处理策略的启动、停止、暂停等控制操作
"""

import sys
import os
from PySide6.QtWidgets import QMessageBox, QDialog
from 窗口UI布局.UI管理.策略窗口模块.类_策略配置窗口 import 类_策略配置窗口
from 窗口UI布局.UI管理.策略窗口模块.类_策略编辑窗口 import 类_策略编辑窗口
from 数据库.数据访问.类_表格列映射管理 import 类_中控表格列
import logging

# 获取当前模块的日志记录器
logger = logging.getLogger(__name__)

# 导入简单线程管理器
try:
    # 添加项目根目录到sys.path
    当前目录 = os.path.dirname(os.path.abspath(__file__))
    项目根目录 = os.path.dirname(os.path.dirname(os.path.dirname(当前目录)))
    if 项目根目录 not in sys.path:
        sys.path.insert(0, 项目根目录)

    from 模块类.功能类.类_线程管理 import 类_多线程管理器
    logger.info("多线程管理器导入成功")
except ImportError as e:
    logger.error(f"无法导入多线程管理器: {e}", exc_info=True)
    类_多线程管理器 = None

class 类_策略控制管理:
    """
    策略控制管理类
    负责处理策略的启动、停止、暂停等控制操作
    """
    
    def __init__(self, 参_主窗口):
        """
        初始化策略控制管理器

        @param 参_主窗口: 主窗口实例
        @return None
        @exception ImportError: 当无法导入多线程管理器时抛出
        """
        # 步骤1: 保存主窗口引用
        # 将传入的主窗口实例保存到实例变量中，供后续方法使用
        self.主窗口 = 参_主窗口

        # 步骤2: 初始化线程管理器
        # 检查多线程管理器是否可用，如果可用则创建实例
        # 如果不可用则设置为None并记录警告日志
        if 类_多线程管理器:
            # 获取主窗口的线程安全UI更新器引用
            UI更新器 = getattr(参_主窗口, '线程安全UI更新器', None)
            self.线程管理器 = 类_多线程管理器(参_UI更新器=UI更新器)
            logger.info("线程管理器初始化成功")
        else:
            self.线程管理器 = None
            logger.warning("线程管理器初始化失败")

        logger.info("策略控制管理类初始化完成")
    
    def 启动所有策略(self) -> None:
        """
        启动所有策略的业务逻辑

        @return None
        @exception Exception: 执行过程中可能发生的未知错误
        """
        # 步骤1: 记录操作日志
        # 记录开始执行启动所有策略的操作
        logger.info("执行启动所有策略的业务逻辑")
        pass
    
    def 停止所有策略(self) -> None:
        """
        停止所有策略的业务逻辑

        @return None
        @exception Exception: 执行过程中可能发生的未知错误
        """
        # 步骤1: 记录操作日志
        # 记录开始执行停止所有策略的操作
        logger.info("执行停止所有策略的业务逻辑")
        pass
    
    def 暂停所有策略补单(self) -> None:
        """
        暂停所有策略补单的业务逻辑

        @return None
        @exception Exception: 执行过程中可能发生的未知错误
        """
        # 步骤1: 记录操作日志
        # 记录开始执行暂停所有策略补单的操作
        logger.info("执行暂停所有策略补单的业务逻辑")
        pass
    
    def 恢复所有策略补单(self) -> None:
        """
        恢复所有策略补单的业务逻辑

        @return None
        @exception Exception: 执行过程中可能发生的未知错误
        """
        # 步骤1: 记录操作日志
        # 记录开始执行恢复所有策略补单的操作
        logger.info("执行恢复所有策略补单的业务逻辑")
        pass
    
    def 执行一键平仓(self) -> None:
        """
        执行一键平仓的业务逻辑

        @return None
        @exception Exception: 执行过程中可能发生的未知错误
        """
        # 步骤1: 记录操作日志
        # 记录开始执行一键平仓的操作
        logger.info("执行一键平仓的业务逻辑")
        pass
    
    def 单个启动(self, 参_行号: int) -> None:
        """
        启动单个机器人

        @param 参_行号 (int): 表格行号
        @return None
        @exception ValueError: 当无法获取机器人信息时抛出
        @exception Exception: 执行过程中可能发生的未知错误
        """
        # 步骤1: 检查线程管理器可用性
        # 验证线程管理器是否已正确初始化，如果不可用则直接返回
        if not self.线程管理器:
            print("线程管理器不可用")
            return

        try:
            # 步骤2: 获取机器人信息
            # 从表格中获取机器人编号和交易对信息
            # 机器人编号在第25列（索引24），交易对在第2列（索引1）
            机器人编号项 = self.主窗口.tableWidget_control.item(参_行号, 类_中控表格列.列_机器人编号)
            交易对项 = self.主窗口.tableWidget_control.item(参_行号, 类_中控表格列.列_交易对)

            # 步骤3: 验证数据完整性
            # 检查是否成功获取到机器人编号和交易对信息
            # 如果任一信息缺失，则抛出异常并停止执行
            if not 机器人编号项 or not 交易对项:
                print("无法获取机器人信息")
                return

            机器人编号 = 机器人编号项.text()
            交易对 = 交易对项.text()

            # 步骤4: 启动线程
            # 调用线程管理器的启动线程方法
            # 根据返回结果判断启动是否成功
            if self.线程管理器.启动线程(机器人编号, 交易对):
                print(f"成功启动机器人: {机器人编号} - {交易对}")
                logger.info(f"成功启动机器人: {机器人编号} - {交易对}")
                self.主窗口.显示状态栏消息(f"机器人 {机器人编号} 已启动")
            else:
                print(f"启动机器人失败: {机器人编号}")
                self.主窗口.显示状态栏消息(f"机器人 {机器人编号} 启动失败")

        except Exception as e:
            print(f"启动机器人出错: {e}")
            logger.error(f"启动机器人出错: {e}")
    
    def 单个停止(self, 参_行号: int) -> None:
        """
        停止单个机器人

        @param 参_行号 (int): 表格行号
        @return None
        @exception ValueError: 当无法获取机器人编号时抛出
        @exception Exception: 执行过程中可能发生的未知错误
        """
        # 步骤1: 检查线程管理器可用性
        # 验证线程管理器是否已正确初始化，如果不可用则直接返回
        if not self.线程管理器:
            print("线程管理器不可用")
            return

        try:
            # 步骤2: 获取机器人编号
            # 从表格中获取机器人编号信息
            # 机器人编号在第25列（索引24）
            机器人编号项 = self.主窗口.tableWidget_control.item(参_行号, 类_中控表格列.列_机器人编号)

            # 步骤3: 验证数据完整性
            # 检查是否成功获取到机器人编号信息
            # 如果信息缺失，则抛出异常并停止执行
            if not 机器人编号项:
                print("无法获取机器人编号")
                return

            机器人编号 = 机器人编号项.text()

            # 步骤4: 停止线程
            # 调用线程管理器的停止线程方法
            # 根据返回结果判断停止是否成功
            if self.线程管理器.停止线程(机器人编号):
                print(f"成功停止机器人: {机器人编号}")
                logger.info(f"成功停止机器人: {机器人编号}")
                self.主窗口.显示状态栏消息(f"机器人 {机器人编号} 已停止")
            else:
                print(f"停止机器人失败: {机器人编号}")
                self.主窗口.显示状态栏消息(f"机器人 {机器人编号} 停止失败")

        except Exception as e:
            print(f"停止机器人出错: {e}")
            logger.error(f"停止机器人出错: {e}")
    
    def 单个暂停补单(self, 参_行号: int) -> None:
        """
        暂停单个机器人的补单功能
        
        @param 参_行号 (int): 表格行号
        @return None
        @exception Exception: 执行过程中可能发生的未知错误
        """
        # 步骤1: 获取机器人编号
        # 从表格中获取机器人编号信息
        # 机器人编号在第25列（索引24）
        机器人编号项 = self.主窗口.tableWidget_control.item(参_行号, 类_中控表格列.列_机器人编号)
        
        # 步骤2: 执行暂停操作
        # 如果成功获取到机器人编号，则执行暂停补单操作
        # 记录操作日志并更新状态栏消息
        if 机器人编号项:
            机器人编号 = 机器人编号项.text()
            logger.info(f"暂停机器人补单: {机器人编号}")
            self.主窗口.显示状态栏消息(f"机器人 {机器人编号} 补单已暂停")
    
    def 单个恢复补单(self, 参_行号: int) -> None:
        """
        恢复单个机器人的补单功能
        
        @param 参_行号 (int): 表格行号
        @return None
        @exception Exception: 执行过程中可能发生的未知错误
        """
        # 步骤1: 获取机器人编号
        # 从表格中获取机器人编号信息
        # 机器人编号在第25列（索引24）
        机器人编号项 = self.主窗口.tableWidget_control.item(参_行号, 类_中控表格列.列_机器人编号)
        
        # 步骤2: 执行恢复操作
        # 如果成功获取到机器人编号，则执行恢复补单操作
        # 记录操作日志并更新状态栏消息
        if 机器人编号项:
            机器人编号 = 机器人编号项.text()
            logger.info(f"恢复机器人补单: {机器人编号}")
            self.主窗口.显示状态栏消息(f"机器人 {机器人编号} 补单已恢复")
    
    def 单个平仓(self, 参_行号: int) -> None:
        """
        平仓单个机器人的所有持仓
        
        @param 参_行号 (int): 表格行号
        @return None
        @exception Exception: 执行过程中可能发生的未知错误
        """
        # 步骤1: 获取机器人编号
        # 从表格中获取机器人编号信息
        # 机器人编号在第25列（索引24）
        机器人编号项 = self.主窗口.tableWidget_control.item(参_行号, 类_中控表格列.列_机器人编号)
        
        # 步骤2: 显示确认对话框
        # 如果成功获取到机器人编号，则显示确认对话框
        # 询问用户是否确定要平仓该机器人的所有持仓
        if 机器人编号项:
            机器人编号 = 机器人编号项.text()
            logger.info(f"准备平仓机器人: {机器人编号}")
            
            # 创建确认对话框
            # 使用QMessageBox.question显示确认对话框
            # 用户可以选择Yes或No
            局_回复 = QMessageBox.question(
                self.主窗口, "确认操作", 
                f"确定要平仓机器人 {机器人编号} 的所有持仓吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            # 步骤3: 执行平仓操作
            # 如果用户确认平仓，则执行平仓操作
            # 记录操作日志并更新状态栏消息
            if 局_回复 == QMessageBox.StandardButton.Yes:
                logger.info(f"执行平仓机器人: {机器人编号}")
                self.主窗口.显示状态栏消息(f"机器人 {机器人编号} 的所有持仓已平仓")
    
    def 单个删除(self, 参_行号: int) -> None:
        """
        删除单个机器人
        
        @param 参_行号 (int): 表格行号
        @return None
        @exception Exception: 执行过程中可能发生的未知错误
        """
        # 步骤1: 获取机器人编号
        # 从表格中获取机器人编号信息
        # 机器人编号在第25列（索引24）
        机器人编号项 = self.主窗口.tableWidget_control.item(参_行号, 类_中控表格列.列_机器人编号)
        
        # 步骤2: 显示确认对话框
        # 如果成功获取到机器人编号，则显示确认对话框
        # 询问用户是否确定要删除该机器人
        if 机器人编号项:
            机器人编号 = 机器人编号项.text()
            logger.info(f"准备删除机器人: {机器人编号}")
            
            # 创建确认对话框
            # 使用QMessageBox.warning显示警告确认对话框
            # 提醒用户此操作不可撤销
            局_回复 = QMessageBox.warning(
                self.主窗口, "确认删除", 
                f"确定要删除机器人 {机器人编号} 吗？\n此操作不可撤销！",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            # 步骤3: 执行删除操作
            # 如果用户确认删除，则执行删除操作
            if 局_回复 == QMessageBox.StandardButton.Yes:
                # 步骤3.1: 从数据库中删除机器人
                # 检查数据库管理器是否可用，如果可用则调用删除方法
                if self.主窗口.类_数据库管理:
                    # 调用数据库管理器删除机器人
                    # 根据返回结果判断删除是否成功
                    删除成功 = self.主窗口.类_数据库管理.删除机器人(机器人编号)
                    if not 删除成功:
                        logger.error(f"机器人 {机器人编号} 删除失败")
                        QMessageBox.critical(self.主窗口, "删除失败", f"机器人 {机器人编号} 删除失败！")
                        return
                
                # 步骤3.2: 从表格中移除该行
                # 从UI表格中移除对应的行，更新界面显示
                self.主窗口.tableWidget_control.removeRow(参_行号)
                
                # 步骤3.3: 记录操作结果
                # 记录删除成功的日志并更新状态栏消息
                logger.info(f"机器人 {机器人编号} 删除成功")
                self.主窗口.显示状态栏消息(f"机器人 {机器人编号} 已删除")
    
    def 新建机器人(self) -> None:
        """
        新建机器人

        @return None
        @exception Exception: 执行过程中可能发生的未知错误
        """
        # 步骤1: 记录操作日志
        # 记录开始新建机器人的操作
        logger.info("开始新建机器人")
        
        # 步骤2: 创建策略配置窗口
        # 创建策略配置窗口实例，传入主窗口作为父窗口
        策略配置窗口 = 类_策略配置窗口(self.主窗口)
        
        # 步骤3: 显示配置窗口
        # 以模态方式显示策略配置窗口
        # 如果用户点击确定，则执行后续操作
        if 策略配置窗口.exec() == QDialog.DialogCode.Accepted:
            logger.info("策略配置已保存")
            # 步骤4: 刷新表格数据
            # 刷新中控表格数据，显示新创建的机器人
            self.主窗口.表格数据管理.刷新中控表格数据()
            self.主窗口.显示状态栏消息("新策略已创建")
    
    def 修改机器人(self, 参_行号: int) -> None:
        """
        修改机器人
        
        @param 参_行号 (int): 表格行号
        @return None
        @exception Exception: 执行过程中可能发生的未知错误
        """
        # 步骤1: 获取机器人信息
        # 从表格中获取机器人编号和策略配置信息
            # 机器人编号在第25列（索引24），策略配置在第20列（索引19）
        机器人编号项 = self.主窗口.tableWidget_control.item(参_行号, 类_中控表格列.列_机器人编号)
        if 机器人编号项:
            机器人编号 = 机器人编号项.text()
            
            # 获取策略配置类型
            策略配置项 = self.主窗口.tableWidget_control.item(参_行号, 类_中控表格列.列_策略配置)
            策略配置 = 策略配置项.text() if 策略配置项 else "未知"
            
            logger.info(f"开始修改机器人: {机器人编号}, 策略配置: {策略配置}")
            
            # 步骤2: 根据策略类型显示提示
            # 根据不同的策略配置类型显示相应的提示信息
            if 策略配置 == "AI策略":
                print("这是AI策略，将显示AI策略编辑界面")
            elif 策略配置 == "自定义":
                print("这是自定义策略，将显示自定义策略编辑界面")
            else:
                print(f"未知的策略配置类型: {策略配置}")
            
            # 步骤3: 创建策略编辑窗口
            # 创建策略编辑窗口实例，传入机器人编号和主窗口
            策略编辑窗口 = 类_策略编辑窗口(机器人编号, self.主窗口)
            
            # 步骤4: 显示编辑窗口
            # 以模态方式显示策略编辑窗口
            # 如果用户点击确定，则执行后续操作
            if 策略编辑窗口.exec() == QDialog.DialogCode.Accepted:
                print(f"策略 {机器人编号} 已更新")
                # 步骤5: 刷新表格数据
                # 刷新中控表格数据，显示更新后的机器人信息
                self.主窗口.表格数据管理.刷新中控表格数据()
                self.主窗口.显示状态栏消息(f"策略 {机器人编号} 已更新") 