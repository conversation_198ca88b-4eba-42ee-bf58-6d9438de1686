# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'Strategy_Config.ui'
##
## Created by: Qt User Interface Compiler version 6.9.1
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (Q<PERSON>rush, QColor, QC<PERSON>alGradient, QCursor,
    QFont, QFontDatabase, QGradient, QIcon,
    QImage, QKeySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QCheckBox, QComboBox, QDialog,
    QDoubleSpinBox, QGroupBox, QHBoxLayout, QLabel,
    QPushButton, QRadioButton, QScrollArea, QSizePolicy,
    QSpacerItem, QSpinBox, QTabWidget, QVBoxLayout,
    QWidget)

class Ui_StrategyConfigWindow(object):
    def setupUi(self, StrategyConfigWindow):
        if not StrategyConfigWindow.objectName():
            StrategyConfigWindow.setObjectName(u"StrategyConfigWindow")
        StrategyConfigWindow.resize(650, 500)
        self.verticalLayout_main = QVBoxLayout(StrategyConfigWindow)
        self.verticalLayout_main.setSpacing(8)
        self.verticalLayout_main.setObjectName(u"verticalLayout_main")
        self.verticalLayout_main.setContentsMargins(10, 10, 10, 10)
        self.groupBox_trading_pair = QGroupBox(StrategyConfigWindow)
        self.groupBox_trading_pair.setObjectName(u"groupBox_trading_pair")
        self.horizontalLayout_trading_pair = QHBoxLayout(self.groupBox_trading_pair)
        self.horizontalLayout_trading_pair.setSpacing(6)
        self.horizontalLayout_trading_pair.setObjectName(u"horizontalLayout_trading_pair")
        self.horizontalLayout_trading_pair.setContentsMargins(6, 6, 6, 6)
        self.comboBox_trading_pair = QComboBox(self.groupBox_trading_pair)
        self.comboBox_trading_pair.setObjectName(u"comboBox_trading_pair")
        self.comboBox_trading_pair.setMinimumSize(QSize(200, 0))
        self.comboBox_trading_pair.setEditable(True)

        self.horizontalLayout_trading_pair.addWidget(self.comboBox_trading_pair)

        self.pushButton_add_all = QPushButton(self.groupBox_trading_pair)
        self.pushButton_add_all.setObjectName(u"pushButton_add_all")
        self.pushButton_add_all.setMinimumSize(QSize(100, 30))

        self.horizontalLayout_trading_pair.addWidget(self.pushButton_add_all)

        self.horizontalSpacer_trading_pair = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_trading_pair.addItem(self.horizontalSpacer_trading_pair)


        self.verticalLayout_main.addWidget(self.groupBox_trading_pair)

        self.tabWidget_strategy = QTabWidget(StrategyConfigWindow)
        self.tabWidget_strategy.setObjectName(u"tabWidget_strategy")
        self.tab_custom_strategy = QWidget()
        self.tab_custom_strategy.setObjectName(u"tab_custom_strategy")
        self.verticalLayout_custom = QVBoxLayout(self.tab_custom_strategy)
        self.verticalLayout_custom.setSpacing(6)
        self.verticalLayout_custom.setObjectName(u"verticalLayout_custom")
        self.scrollArea_custom = QScrollArea(self.tab_custom_strategy)
        self.scrollArea_custom.setObjectName(u"scrollArea_custom")
        self.scrollArea_custom.setWidgetResizable(True)
        self.scrollAreaWidgetContents_custom = QWidget()
        self.scrollAreaWidgetContents_custom.setObjectName(u"scrollAreaWidgetContents_custom")
        self.scrollAreaWidgetContents_custom.setGeometry(QRect(0, -110, 592, 435))
        self.verticalLayout_custom_content = QVBoxLayout(self.scrollAreaWidgetContents_custom)
        self.verticalLayout_custom_content.setSpacing(6)
        self.verticalLayout_custom_content.setObjectName(u"verticalLayout_custom_content")
        self.verticalLayout_custom_content.setContentsMargins(6, 6, 6, 6)
        self.groupBox_first_order = QGroupBox(self.scrollAreaWidgetContents_custom)
        self.groupBox_first_order.setObjectName(u"groupBox_first_order")
        self.horizontalLayout_first_order = QHBoxLayout(self.groupBox_first_order)
        self.horizontalLayout_first_order.setSpacing(6)
        self.horizontalLayout_first_order.setObjectName(u"horizontalLayout_first_order")
        self.horizontalLayout_first_order.setContentsMargins(6, 4, 6, 4)
        self.doubleSpinBox_first_amount = QDoubleSpinBox(self.groupBox_first_order)
        self.doubleSpinBox_first_amount.setObjectName(u"doubleSpinBox_first_amount")
        self.doubleSpinBox_first_amount.setMinimumSize(QSize(120, 0))
        self.doubleSpinBox_first_amount.setMaximum(99999.000000000000000)
        self.doubleSpinBox_first_amount.setValue(10.000000000000000)

        self.horizontalLayout_first_order.addWidget(self.doubleSpinBox_first_amount)

        self.horizontalSpacer_first_order = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_first_order.addItem(self.horizontalSpacer_first_order)


        self.verticalLayout_custom_content.addWidget(self.groupBox_first_order)

        self.groupBox_drop_percent = QGroupBox(self.scrollAreaWidgetContents_custom)
        self.groupBox_drop_percent.setObjectName(u"groupBox_drop_percent")
        self.horizontalLayout_drop_percent = QHBoxLayout(self.groupBox_drop_percent)
        self.horizontalLayout_drop_percent.setSpacing(6)
        self.horizontalLayout_drop_percent.setObjectName(u"horizontalLayout_drop_percent")
        self.horizontalLayout_drop_percent.setContentsMargins(6, 4, 6, 4)
        self.doubleSpinBox_drop_percent = QDoubleSpinBox(self.groupBox_drop_percent)
        self.doubleSpinBox_drop_percent.setObjectName(u"doubleSpinBox_drop_percent")
        self.doubleSpinBox_drop_percent.setMinimumSize(QSize(120, 0))
        self.doubleSpinBox_drop_percent.setMaximum(100.000000000000000)
        self.doubleSpinBox_drop_percent.setValue(1.000000000000000)

        self.horizontalLayout_drop_percent.addWidget(self.doubleSpinBox_drop_percent)

        self.horizontalSpacer_drop_percent = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_drop_percent.addItem(self.horizontalSpacer_drop_percent)


        self.verticalLayout_custom_content.addWidget(self.groupBox_drop_percent)

        self.groupBox_position_multiplier = QGroupBox(self.scrollAreaWidgetContents_custom)
        self.groupBox_position_multiplier.setObjectName(u"groupBox_position_multiplier")
        self.horizontalLayout_position_multiplier = QHBoxLayout(self.groupBox_position_multiplier)
        self.horizontalLayout_position_multiplier.setSpacing(6)
        self.horizontalLayout_position_multiplier.setObjectName(u"horizontalLayout_position_multiplier")
        self.horizontalLayout_position_multiplier.setContentsMargins(6, 4, 6, 4)
        self.doubleSpinBox_position_multiplier = QDoubleSpinBox(self.groupBox_position_multiplier)
        self.doubleSpinBox_position_multiplier.setObjectName(u"doubleSpinBox_position_multiplier")
        self.doubleSpinBox_position_multiplier.setMinimumSize(QSize(120, 0))
        self.doubleSpinBox_position_multiplier.setMinimum(1.000000000000000)
        self.doubleSpinBox_position_multiplier.setMaximum(10.000000000000000)
        self.doubleSpinBox_position_multiplier.setValue(1.300000000000000)

        self.horizontalLayout_position_multiplier.addWidget(self.doubleSpinBox_position_multiplier)

        self.horizontalSpacer_position_multiplier = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_position_multiplier.addItem(self.horizontalSpacer_position_multiplier)


        self.verticalLayout_custom_content.addWidget(self.groupBox_position_multiplier)

        self.groupBox_profit_percent = QGroupBox(self.scrollAreaWidgetContents_custom)
        self.groupBox_profit_percent.setObjectName(u"groupBox_profit_percent")
        self.horizontalLayout_profit_percent = QHBoxLayout(self.groupBox_profit_percent)
        self.horizontalLayout_profit_percent.setSpacing(6)
        self.horizontalLayout_profit_percent.setObjectName(u"horizontalLayout_profit_percent")
        self.horizontalLayout_profit_percent.setContentsMargins(6, 4, 6, 4)
        self.doubleSpinBox_profit_percent = QDoubleSpinBox(self.groupBox_profit_percent)
        self.doubleSpinBox_profit_percent.setObjectName(u"doubleSpinBox_profit_percent")
        self.doubleSpinBox_profit_percent.setMinimumSize(QSize(120, 0))
        self.doubleSpinBox_profit_percent.setMaximum(100.000000000000000)
        self.doubleSpinBox_profit_percent.setValue(1.500000000000000)

        self.horizontalLayout_profit_percent.addWidget(self.doubleSpinBox_profit_percent)

        self.horizontalSpacer_profit_percent = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_profit_percent.addItem(self.horizontalSpacer_profit_percent)


        self.verticalLayout_custom_content.addWidget(self.groupBox_profit_percent)

        self.groupBox_max_orders = QGroupBox(self.scrollAreaWidgetContents_custom)
        self.groupBox_max_orders.setObjectName(u"groupBox_max_orders")
        self.horizontalLayout_max_orders = QHBoxLayout(self.groupBox_max_orders)
        self.horizontalLayout_max_orders.setSpacing(6)
        self.horizontalLayout_max_orders.setObjectName(u"horizontalLayout_max_orders")
        self.horizontalLayout_max_orders.setContentsMargins(6, 4, 6, 4)
        self.spinBox_max_orders = QSpinBox(self.groupBox_max_orders)
        self.spinBox_max_orders.setObjectName(u"spinBox_max_orders")
        self.spinBox_max_orders.setMinimumSize(QSize(120, 0))
        self.spinBox_max_orders.setMinimum(1)
        self.spinBox_max_orders.setMaximum(100)
        self.spinBox_max_orders.setValue(10)

        self.horizontalLayout_max_orders.addWidget(self.spinBox_max_orders)

        self.horizontalSpacer_max_orders = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_max_orders.addItem(self.horizontalSpacer_max_orders)


        self.verticalLayout_custom_content.addWidget(self.groupBox_max_orders)

        self.groupBox_profit_stop = QGroupBox(self.scrollAreaWidgetContents_custom)
        self.groupBox_profit_stop.setObjectName(u"groupBox_profit_stop")
        self.horizontalLayout_profit_stop = QHBoxLayout(self.groupBox_profit_stop)
        self.horizontalLayout_profit_stop.setSpacing(6)
        self.horizontalLayout_profit_stop.setObjectName(u"horizontalLayout_profit_stop")
        self.horizontalLayout_profit_stop.setContentsMargins(6, 4, 6, 4)
        self.doubleSpinBox_profit_stop = QDoubleSpinBox(self.groupBox_profit_stop)
        self.doubleSpinBox_profit_stop.setObjectName(u"doubleSpinBox_profit_stop")
        self.doubleSpinBox_profit_stop.setMinimumSize(QSize(120, 0))
        self.doubleSpinBox_profit_stop.setMaximum(99999.000000000000000)
        self.doubleSpinBox_profit_stop.setValue(100.000000000000000)

        self.horizontalLayout_profit_stop.addWidget(self.doubleSpinBox_profit_stop)

        self.horizontalSpacer_profit_stop = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_profit_stop.addItem(self.horizontalSpacer_profit_stop)


        self.verticalLayout_custom_content.addWidget(self.groupBox_profit_stop)

        self.groupBox_dynamic_position = QGroupBox(self.scrollAreaWidgetContents_custom)
        self.groupBox_dynamic_position.setObjectName(u"groupBox_dynamic_position")
        self.horizontalLayout_dynamic_position = QHBoxLayout(self.groupBox_dynamic_position)
        self.horizontalLayout_dynamic_position.setSpacing(6)
        self.horizontalLayout_dynamic_position.setObjectName(u"horizontalLayout_dynamic_position")
        self.horizontalLayout_dynamic_position.setContentsMargins(6, 4, 6, 4)
        self.checkBox_dynamic_position = QCheckBox(self.groupBox_dynamic_position)
        self.checkBox_dynamic_position.setObjectName(u"checkBox_dynamic_position")
        self.checkBox_dynamic_position.setChecked(True)

        self.horizontalLayout_dynamic_position.addWidget(self.checkBox_dynamic_position)

        self.horizontalSpacer_dynamic_position = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_dynamic_position.addItem(self.horizontalSpacer_dynamic_position)


        self.verticalLayout_custom_content.addWidget(self.groupBox_dynamic_position)

        self.groupBox_rebound_percent = QGroupBox(self.scrollAreaWidgetContents_custom)
        self.groupBox_rebound_percent.setObjectName(u"groupBox_rebound_percent")
        self.horizontalLayout_rebound_percent = QHBoxLayout(self.groupBox_rebound_percent)
        self.horizontalLayout_rebound_percent.setSpacing(6)
        self.horizontalLayout_rebound_percent.setObjectName(u"horizontalLayout_rebound_percent")
        self.horizontalLayout_rebound_percent.setContentsMargins(6, 4, 6, 4)
        self.doubleSpinBox_rebound_percent = QDoubleSpinBox(self.groupBox_rebound_percent)
        self.doubleSpinBox_rebound_percent.setObjectName(u"doubleSpinBox_rebound_percent")
        self.doubleSpinBox_rebound_percent.setMinimumSize(QSize(120, 0))
        self.doubleSpinBox_rebound_percent.setMaximum(100.000000000000000)
        self.doubleSpinBox_rebound_percent.setValue(0.300000000000000)

        self.horizontalLayout_rebound_percent.addWidget(self.doubleSpinBox_rebound_percent)

        self.horizontalSpacer_rebound_percent = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_rebound_percent.addItem(self.horizontalSpacer_rebound_percent)


        self.verticalLayout_custom_content.addWidget(self.groupBox_rebound_percent)

        self.scrollArea_custom.setWidget(self.scrollAreaWidgetContents_custom)

        self.verticalLayout_custom.addWidget(self.scrollArea_custom)

        self.tabWidget_strategy.addTab(self.tab_custom_strategy, "")
        self.tab_ai_strategy = QWidget()
        self.tab_ai_strategy.setObjectName(u"tab_ai_strategy")
        self.verticalLayout_ai = QVBoxLayout(self.tab_ai_strategy)
        self.verticalLayout_ai.setSpacing(6)
        self.verticalLayout_ai.setObjectName(u"verticalLayout_ai")
        self.groupBox_strategy_selection = QGroupBox(self.tab_ai_strategy)
        self.groupBox_strategy_selection.setObjectName(u"groupBox_strategy_selection")
        self.horizontalLayout_strategy_selection = QHBoxLayout(self.groupBox_strategy_selection)
        self.horizontalLayout_strategy_selection.setSpacing(20)
        self.horizontalLayout_strategy_selection.setObjectName(u"horizontalLayout_strategy_selection")
        self.horizontalLayout_strategy_selection.setContentsMargins(6, 4, 6, 4)
        self.radioButton_aggressive = QRadioButton(self.groupBox_strategy_selection)
        self.radioButton_aggressive.setObjectName(u"radioButton_aggressive")
        self.radioButton_aggressive.setChecked(True)

        self.horizontalLayout_strategy_selection.addWidget(self.radioButton_aggressive)

        self.radioButton_defensive = QRadioButton(self.groupBox_strategy_selection)
        self.radioButton_defensive.setObjectName(u"radioButton_defensive")

        self.horizontalLayout_strategy_selection.addWidget(self.radioButton_defensive)

        self.radioButton_universal = QRadioButton(self.groupBox_strategy_selection)
        self.radioButton_universal.setObjectName(u"radioButton_universal")

        self.horizontalLayout_strategy_selection.addWidget(self.radioButton_universal)

        self.horizontalSpacer_strategy = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_strategy_selection.addItem(self.horizontalSpacer_strategy)


        self.verticalLayout_ai.addWidget(self.groupBox_strategy_selection)

        self.groupBox_sub_strategy = QGroupBox(self.tab_ai_strategy)
        self.groupBox_sub_strategy.setObjectName(u"groupBox_sub_strategy")
        self.verticalLayout_sub_strategy = QVBoxLayout(self.groupBox_sub_strategy)
        self.verticalLayout_sub_strategy.setSpacing(4)
        self.verticalLayout_sub_strategy.setObjectName(u"verticalLayout_sub_strategy")
        self.verticalLayout_sub_strategy.setContentsMargins(6, 4, 6, 4)
        self.horizontalLayout_sub_strategies = QHBoxLayout()
        self.horizontalLayout_sub_strategies.setSpacing(15)
        self.horizontalLayout_sub_strategies.setObjectName(u"horizontalLayout_sub_strategies")
        self.radioButton_universal_a = QRadioButton(self.groupBox_sub_strategy)
        self.radioButton_universal_a.setObjectName(u"radioButton_universal_a")
        self.radioButton_universal_a.setVisible(False)
        self.radioButton_universal_a.setChecked(False)

        self.horizontalLayout_sub_strategies.addWidget(self.radioButton_universal_a)

        self.radioButton_crazy = QRadioButton(self.groupBox_sub_strategy)
        self.radioButton_crazy.setObjectName(u"radioButton_crazy")
        self.radioButton_crazy.setChecked(False)

        self.horizontalLayout_sub_strategies.addWidget(self.radioButton_crazy)

        self.radioButton_aggressive_sub = QRadioButton(self.groupBox_sub_strategy)
        self.radioButton_aggressive_sub.setObjectName(u"radioButton_aggressive_sub")

        self.horizontalLayout_sub_strategies.addWidget(self.radioButton_aggressive_sub)

        self.radioButton_balanced = QRadioButton(self.groupBox_sub_strategy)
        self.radioButton_balanced.setObjectName(u"radioButton_balanced")
        self.radioButton_balanced.setChecked(True)

        self.horizontalLayout_sub_strategies.addWidget(self.radioButton_balanced)

        self.radioButton_stable = QRadioButton(self.groupBox_sub_strategy)
        self.radioButton_stable.setObjectName(u"radioButton_stable")

        self.horizontalLayout_sub_strategies.addWidget(self.radioButton_stable)

        self.radioButton_conservative = QRadioButton(self.groupBox_sub_strategy)
        self.radioButton_conservative.setObjectName(u"radioButton_conservative")

        self.horizontalLayout_sub_strategies.addWidget(self.radioButton_conservative)

        self.horizontalSpacer_sub_strategies = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_sub_strategies.addItem(self.horizontalSpacer_sub_strategies)


        self.verticalLayout_sub_strategy.addLayout(self.horizontalLayout_sub_strategies)


        self.verticalLayout_ai.addWidget(self.groupBox_sub_strategy)

        self.groupBox_capital_setting = QGroupBox(self.tab_ai_strategy)
        self.groupBox_capital_setting.setObjectName(u"groupBox_capital_setting")
        self.horizontalLayout_capital = QHBoxLayout(self.groupBox_capital_setting)
        self.horizontalLayout_capital.setSpacing(6)
        self.horizontalLayout_capital.setObjectName(u"horizontalLayout_capital")
        self.horizontalLayout_capital.setContentsMargins(6, 4, 6, 4)
        self.label_capital = QLabel(self.groupBox_capital_setting)
        self.label_capital.setObjectName(u"label_capital")

        self.horizontalLayout_capital.addWidget(self.label_capital)

        self.doubleSpinBox_capital = QDoubleSpinBox(self.groupBox_capital_setting)
        self.doubleSpinBox_capital.setObjectName(u"doubleSpinBox_capital")
        self.doubleSpinBox_capital.setMinimumSize(QSize(120, 0))
        self.doubleSpinBox_capital.setMaximum(99999.000000000000000)
        self.doubleSpinBox_capital.setValue(100.000000000000000)

        self.horizontalLayout_capital.addWidget(self.doubleSpinBox_capital)

        self.horizontalSpacer_capital = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_capital.addItem(self.horizontalSpacer_capital)


        self.verticalLayout_ai.addWidget(self.groupBox_capital_setting)

        self.verticalSpacer_ai = QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)

        self.verticalLayout_ai.addItem(self.verticalSpacer_ai)

        self.tabWidget_strategy.addTab(self.tab_ai_strategy, "")

        self.verticalLayout_main.addWidget(self.tabWidget_strategy)

        self.horizontalLayout_buttons = QHBoxLayout()
        self.horizontalLayout_buttons.setSpacing(6)
        self.horizontalLayout_buttons.setObjectName(u"horizontalLayout_buttons")
        self.horizontalSpacer_buttons = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_buttons.addItem(self.horizontalSpacer_buttons)

        self.pushButton_add_strategy = QPushButton(StrategyConfigWindow)
        self.pushButton_add_strategy.setObjectName(u"pushButton_add_strategy")
        self.pushButton_add_strategy.setMinimumSize(QSize(100, 30))

        self.horizontalLayout_buttons.addWidget(self.pushButton_add_strategy)

        self.pushButton_cancel = QPushButton(StrategyConfigWindow)
        self.pushButton_cancel.setObjectName(u"pushButton_cancel")
        self.pushButton_cancel.setMinimumSize(QSize(80, 30))

        self.horizontalLayout_buttons.addWidget(self.pushButton_cancel)


        self.verticalLayout_main.addLayout(self.horizontalLayout_buttons)


        self.retranslateUi(StrategyConfigWindow)
        self.pushButton_cancel.clicked.connect(StrategyConfigWindow.reject)

        self.tabWidget_strategy.setCurrentIndex(0)


        QMetaObject.connectSlotsByName(StrategyConfigWindow)
    # setupUi

    def retranslateUi(self, StrategyConfigWindow):
        StrategyConfigWindow.setWindowTitle(QCoreApplication.translate("StrategyConfigWindow", u"\u7b56\u7565\u914d\u7f6e", None))
        self.groupBox_trading_pair.setTitle(QCoreApplication.translate("StrategyConfigWindow", u"\u9009\u62e9\u4ea4\u6613\u5bf9", None))
        self.pushButton_add_all.setText(QCoreApplication.translate("StrategyConfigWindow", u"\u6dfb\u52a0\u5168\u90e8", None))
        self.groupBox_first_order.setTitle(QCoreApplication.translate("StrategyConfigWindow", u"\u9996\u5355\u91d1\u989d", None))
        self.doubleSpinBox_first_amount.setSuffix(QCoreApplication.translate("StrategyConfigWindow", u" U", None))
        self.groupBox_drop_percent.setTitle(QCoreApplication.translate("StrategyConfigWindow", u"\u8dcc\u5e45\u591a\u5c11\u52a0\u4ed3", None))
        self.doubleSpinBox_drop_percent.setSuffix(QCoreApplication.translate("StrategyConfigWindow", u" %", None))
        self.groupBox_position_multiplier.setTitle(QCoreApplication.translate("StrategyConfigWindow", u"\u52a0\u4ed3\u500d\u6570", None))
        self.doubleSpinBox_position_multiplier.setSuffix(QCoreApplication.translate("StrategyConfigWindow", u" \u500d", None))
        self.groupBox_profit_percent.setTitle(QCoreApplication.translate("StrategyConfigWindow", u"\u767e\u5206\u6bd4\u6b62\u76c8\u6bd4\u4f8b", None))
        self.doubleSpinBox_profit_percent.setSuffix(QCoreApplication.translate("StrategyConfigWindow", u" %", None))
        self.groupBox_max_orders.setTitle(QCoreApplication.translate("StrategyConfigWindow", u"\u6700\u5927\u6301\u4ed3\u5355\u6570", None))
        self.spinBox_max_orders.setSuffix(QCoreApplication.translate("StrategyConfigWindow", u" \u6b21", None))
        self.groupBox_profit_stop.setTitle(QCoreApplication.translate("StrategyConfigWindow", u"\u76c8\u5229\u591a\u5c11\u505c\u6b62\u673a\u5668\u4eba", None))
        self.doubleSpinBox_profit_stop.setSuffix(QCoreApplication.translate("StrategyConfigWindow", u" U", None))
        self.groupBox_dynamic_position.setTitle(QCoreApplication.translate("StrategyConfigWindow", u"\u52a8\u6001\u6301\u4ed3", None))
        self.checkBox_dynamic_position.setText(QCoreApplication.translate("StrategyConfigWindow", u"\u5f00\u542f\u52a8\u6001\u6301\u4ed3", None))
        self.groupBox_rebound_percent.setTitle(QCoreApplication.translate("StrategyConfigWindow", u"\u767e\u5206\u6bd4\u53cd\u5f39\u591a\u5c11\u8865\u4ed3", None))
        self.doubleSpinBox_rebound_percent.setSuffix(QCoreApplication.translate("StrategyConfigWindow", u" %", None))
        self.tabWidget_strategy.setTabText(self.tabWidget_strategy.indexOf(self.tab_custom_strategy), QCoreApplication.translate("StrategyConfigWindow", u"\u81ea\u5b9a\u4e49\u7b56\u7565", None))
        self.groupBox_strategy_selection.setTitle(QCoreApplication.translate("StrategyConfigWindow", u"\u7b56\u7565\u7c7b\u578b\u9009\u62e9", None))
        self.radioButton_aggressive.setText(QCoreApplication.translate("StrategyConfigWindow", u"\u8fdb\u653b\u578b\u7b56\u7565", None))
        self.radioButton_defensive.setText(QCoreApplication.translate("StrategyConfigWindow", u"\u9632\u5b88\u578b\u7b56\u7565", None))
        self.radioButton_universal.setText(QCoreApplication.translate("StrategyConfigWindow", u"\u901a\u7528\u578b\u7b56\u7565", None))
        self.groupBox_sub_strategy.setTitle(QCoreApplication.translate("StrategyConfigWindow", u"\u5b50\u7b56\u7565\u9009\u62e9", None))
        self.radioButton_universal_a.setText(QCoreApplication.translate("StrategyConfigWindow", u"A", None))
        self.radioButton_crazy.setText(QCoreApplication.translate("StrategyConfigWindow", u"\u75af\u72c2\u578b", None))
        self.radioButton_aggressive_sub.setText(QCoreApplication.translate("StrategyConfigWindow", u"\u6fc0\u8fdb\u578b", None))
        self.radioButton_balanced.setText(QCoreApplication.translate("StrategyConfigWindow", u"\u5e73\u8861\u578b", None))
        self.radioButton_stable.setText(QCoreApplication.translate("StrategyConfigWindow", u"\u7a33\u5065\u578b", None))
        self.radioButton_conservative.setText(QCoreApplication.translate("StrategyConfigWindow", u"\u4fdd\u5b88\u578b", None))
        self.groupBox_capital_setting.setTitle(QCoreApplication.translate("StrategyConfigWindow", u"\u6295\u5165\u672c\u91d1\u8bbe\u7f6e", None))
        self.label_capital.setText(QCoreApplication.translate("StrategyConfigWindow", u"\u6295\u5165\u672c\u91d1:", None))
        self.doubleSpinBox_capital.setSuffix(QCoreApplication.translate("StrategyConfigWindow", u" U", None))
        self.tabWidget_strategy.setTabText(self.tabWidget_strategy.indexOf(self.tab_ai_strategy), QCoreApplication.translate("StrategyConfigWindow", u"AI\u7b56\u7565", None))
        self.pushButton_add_strategy.setText(QCoreApplication.translate("StrategyConfigWindow", u"\u6dfb\u52a0\u7b56\u7565", None))
        self.pushButton_cancel.setText(QCoreApplication.translate("StrategyConfigWindow", u"\u53d6\u6d88", None))
    # retranslateUi

