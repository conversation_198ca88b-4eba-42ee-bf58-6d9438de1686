# -*- coding: utf-8 -*-
"""
API配置管理模块
负责处理交易所API的配置管理
"""

from PySide6.QtWidgets import QMessageBox
import logging
from 模块类.功能类.类_加密工具 import 类_加密工具

# 获取当前模块的日志记录器
logger = logging.getLogger(__name__)

class 类_API配置管理:
    """
    API配置管理类
    负责处理交易所API的配置管理
    """
    
    def __init__(self, 参_主窗口):
        """
        初始化API配置管理器
        
        参数:
            参_主窗口: 主窗口实例
        """
        self.主窗口 = 参_主窗口
        self.加密工具 = 类_加密工具()
        logger.info("API配置管理类初始化完成")
    
    def 保存币安API(self):
        """
        保存币安API配置
        """
        logger.info("开始保存币安API配置")
        
        # 获取输入的API信息
        局_api_key = self.主窗口.lineEdit_Binance_AKEY.text().strip()
        局_secret_key = self.主窗口.lineEdit_Binance_SKEY.text().strip()
        
        # 验证输入
        if not 局_api_key or not 局_secret_key:
            QMessageBox.warning(self.主窗口, "输入错误", "请输入完整的API Key和Secret Key！")
            return
        
        # 加密API信息
        加密_api_key = self.加密工具.加密(局_api_key)
        加密_secret_key = self.加密工具.加密(局_secret_key)
        
        # 调用数据库管理器保存加密后的API配置
        if self.主窗口.类_数据库管理:
            局_保存成功 = self.主窗口.类_数据库管理.保存API配置("币安", 加密_api_key, 加密_secret_key)
        else:
            局_保存成功 = self.保存API配置("币安", 加密_api_key, 加密_secret_key)
        
        if 局_保存成功:
            self.主窗口.显示状态栏消息("币安API配置已保存")
            logger.info("币安API配置保存成功")
        else:
            QMessageBox.critical(self.主窗口, "保存失败", "币安API配置保存失败！")
            logger.error("币安API配置保存失败")
    
    def 保存OKX_API(self):
        """
        保存OKX API配置
        """
        logger.info("开始保存OKX API配置")
        
        # 获取输入的API信息
        局_api_key = self.主窗口.lineEdit_OKX_AKEY.text().strip()
        局_secret_key = self.主窗口.lineEdit_OKX_SKEY.text().strip()
        局_passphrase = self.主窗口.lineEdit_OKX_PASS.text().strip()
        
        # 验证输入
        if not 局_api_key or not 局_secret_key or not 局_passphrase:
            QMessageBox.warning(self.主窗口, "输入错误", "请输入完整的API Key、Secret Key和Passphrase！")
            return
        
        # 加密API信息
        加密_api_key = self.加密工具.加密(局_api_key)
        加密_secret_key = self.加密工具.加密(局_secret_key)
        加密_passphrase = self.加密工具.加密(局_passphrase)
        
        # 调用数据库管理器保存加密后的API配置
        if self.主窗口.类_数据库管理:
            局_保存成功 = self.主窗口.类_数据库管理.保存API配置("OKX", 加密_api_key, 加密_secret_key, 加密_passphrase)
        else:
            局_保存成功 = self.保存API配置("OKX", 加密_api_key, 加密_secret_key, 加密_passphrase)
        
        if 局_保存成功:
            self.主窗口.显示状态栏消息("OKX API配置已保存")
            logger.info("OKX API配置保存成功")
        else:
            QMessageBox.critical(self.主窗口, "保存失败", "OKX API配置保存失败！")
            logger.error("OKX API配置保存失败")
    
    def 保存API配置(self, 参_交易所, 参_api_key, 参_secret_key, 参_passphrase=None):
        """
        保存API配置的通用方法
        
        参数:
            参_交易所: 交易所名称
            参_api_key: 加密后的API Key
            参_secret_key: 加密后的Secret Key
            参_passphrase: 加密后的Passphrase（可选）
            
        返回:
            bool: 保存是否成功
        """
        logger.debug(f"保存{参_交易所}的API配置")
        # TODO: 调用配置管理器保存API配置
        return True  # 临时返回True，实际应该返回保存结果
    
    def 加载API配置(self):
        """
        从数据库加载API配置
        """
        if not self.主窗口.类_数据库管理:
            logger.warning("数据库管理不可用，无法加载API配置")
            return
        
        try:
            # 加载币安API配置
            币安配置 = self.主窗口.类_数据库管理.获取API配置("币安")
            if 币安配置:
                # 解密API信息
                解密_api_key = self.加密工具.解密(币安配置.get("api_key", ""))
                解密_secret_key = self.加密工具.解密(币安配置.get("secret_key", ""))
                self.主窗口.lineEdit_Binance_AKEY.setText(解密_api_key)
                self.主窗口.lineEdit_Binance_SKEY.setText(解密_secret_key)
            
            # 加载OKX API配置
            OKX配置 = self.主窗口.类_数据库管理.获取API配置("OKX")
            if OKX配置:
                # 解密API信息
                解密_api_key = self.加密工具.解密(OKX配置.get("api_key", ""))
                解密_secret_key = self.加密工具.解密(OKX配置.get("secret_key", ""))
                解密_passphrase = self.加密工具.解密(OKX配置.get("passphrase", ""))
                self.主窗口.lineEdit_OKX_AKEY.setText(解密_api_key)
                self.主窗口.lineEdit_OKX_SKEY.setText(解密_secret_key)
                self.主窗口.lineEdit_OKX_PASS.setText(解密_passphrase)
            
            logger.info("API配置加载完成")
        except Exception as e:
            logger.error(f"加载API配置失败: {e}", exc_info=True)
            QMessageBox.warning(self.主窗口, "加载失败", f"加载API配置失败：{e}") 