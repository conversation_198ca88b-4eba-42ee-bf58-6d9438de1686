# 量化交易系统 - 重构版本

## 项目架构说明

本项目采用**依赖注入容器 + 分层架构**的设计模式，实现了高内聚、低耦合的代码结构。

## 目录结构

```
重构版本/
├── README.md                    # 项目说明文档
├── main.py                      # 应用程序入口
├── requirements.txt             # 依赖包列表
│
├── src/                         # 源代码目录
│   ├── __init__.py
│   │
│   ├── core/                    # 核心基础设施
│   │   ├── __init__.py
│   │   ├── container.py         # 依赖注入容器
│   │   ├── interfaces.py        # 核心接口定义
│   │   ├── events.py           # 事件系统
│   │   └── exceptions.py       # 自定义异常
│   │
│   ├── infrastructure/          # 基础设施层
│   │   ├── __init__.py
│   │   ├── database/           # 数据库相关
│   │   │   ├── __init__.py
│   │   │   ├── services.py     # 数据库服务实现
│   │   │   ├── repositories.py # 数据仓储实现
│   │   │   └── models.py       # 数据模型
│   │   │
│   │   ├── external/           # 外部API
│   │   │   ├── __init__.py
│   │   │   ├── okx/           # OKX交易所API
│   │   │   ├── binance/       # 币安交易所API
│   │   │   └── base.py        # API基类
│   │   │
│   │   ├── threading/          # 线程管理
│   │   │   ├── __init__.py
│   │   │   ├── services.py     # 线程服务实现
│   │   │   └── managers.py     # 线程管理器
│   │   │
│   │   └── logging/            # 日志系统
│   │       ├── __init__.py
│   │       └── services.py     # 日志服务
│   │
│   ├── domain/                  # 领域层（业务逻辑）
│   │   ├── __init__.py
│   │   ├── entities/           # 实体对象
│   │   │   ├── __init__.py
│   │   │   ├── robot.py        # 机器人实体
│   │   │   ├── trade.py        # 交易实体
│   │   │   └── strategy.py     # 策略实体
│   │   │
│   │   ├── services/           # 领域服务
│   │   │   ├── __init__.py
│   │   │   ├── trade_service.py      # 交易服务
│   │   │   ├── strategy_service.py   # 策略服务
│   │   │   ├── risk_service.py       # 风险管理服务
│   │   │   └── calculation_service.py # 核心计算服务
│   │   │
│   │   └── repositories/       # 仓储接口
│   │       ├── __init__.py
│   │       ├── robot_repository.py   # 机器人仓储接口
│   │       └── trade_repository.py   # 交易仓储接口
│   │
│   ├── application/             # 应用层
│   │   ├── __init__.py
│   │   ├── services/           # 应用服务
│   │   │   ├── __init__.py
│   │   │   ├── robot_app_service.py  # 机器人应用服务
│   │   │   ├── trade_app_service.py  # 交易应用服务
│   │   │   └── ui_update_service.py  # UI更新服务
│   │   │
│   │   ├── handlers/           # 事件处理器
│   │   │   ├── __init__.py
│   │   │   ├── trade_handlers.py     # 交易事件处理
│   │   │   └── ui_handlers.py        # UI事件处理
│   │   │
│   │   └── bootstrap.py        # 应用启动配置
│   │
│   └── presentation/            # 表现层（UI）
│       ├── __init__.py
│       ├── ui_files/           # UI设计文件（从原项目复制）
│       │   ├── __init__.py
│       │   └── *.py            # Designer生成的UI文件
│       │
│       ├── windows/            # 窗口管理
│       │   ├── __init__.py
│       │   ├── main_window.py  # 主窗口
│       │   └── dialogs/        # 对话框
│       │
│       ├── controllers/        # 控制器
│       │   ├── __init__.py
│       │   ├── main_controller.py    # 主控制器
│       │   ├── trade_controller.py   # 交易控制器
│       │   └── strategy_controller.py # 策略控制器
│       │
│       └── services/           # UI服务
│           ├── __init__.py
│           ├── ui_update_service.py  # UI更新服务实现
│           └── notification_service.py # 通知服务
│
├── tests/                       # 测试目录
│   ├── __init__.py
│   ├── unit/                   # 单元测试
│   ├── integration/            # 集成测试
│   └── fixtures/               # 测试数据
│
├── config/                      # 配置目录
│   ├── __init__.py
│   ├── settings.py             # 配置设置
│   └── logging.yaml            # 日志配置
│
└── docs/                        # 文档目录
    ├── architecture.md         # 架构文档
    ├── api.md                  # API文档
    └── migration.md            # 迁移指南
```

## 核心设计原则

### 1. 依赖注入（Dependency Injection）
- 所有组件通过构造函数注入依赖
- 使用接口而非具体实现
- 便于单元测试和模块替换

### 2. 分层架构（Layered Architecture）
- **表现层**：负责用户界面和用户交互
- **应用层**：协调领域对象完成应用任务
- **领域层**：包含业务逻辑和业务规则
- **基础设施层**：提供技术能力支持

### 3. 事件驱动（Event-Driven）
- 模块间通过事件进行解耦通信
- 支持异步处理和多线程
- 便于功能扩展和监控

### 4. 单一职责（Single Responsibility）
- 每个类只负责一个职责
- 接口设计遵循ISP原则
- 便于维护和测试

## 与原版本的主要差异

### 依赖管理
- **原版本**：层层传递对象引用，紧耦合
- **重构版本**：通过DI容器管理依赖，松耦合

### 架构清晰度
- **原版本**：职责边界模糊，类职责过多
- **重构版本**：清晰的分层架构，单一职责

### 扩展性
- **原版本**：添加新功能需要修改多个类
- **重构版本**：通过接口和事件轻松扩展

### 测试性
- **原版本**：难以进行单元测试
- **重构版本**：支持依赖注入，易于测试

## 快速开始

```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 运行应用
python main.py

# 3. 运行测试
python -m pytest tests/
```

## 开发指南

### 添加新服务
1. 在 `src/core/interfaces.py` 中定义接口
2. 在相应层实现服务
3. 在 `src/application/bootstrap.py` 中注册服务
4. 编写单元测试

### 添加新UI功能
1. 使用Designer设计UI文件
2. 创建对应的控制器
3. 通过DI容器获取所需服务
4. 处理用户交互和事件

## 注意事项

- UI文件保持原样，不要手动修改
- 所有业务逻辑放在领域层和应用层
- 使用事件进行跨层通信
- 遵循接口隔离原则
