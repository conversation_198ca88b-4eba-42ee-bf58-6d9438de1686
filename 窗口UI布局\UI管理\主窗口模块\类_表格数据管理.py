# -*- coding: utf-8 -*-
"""
表格数据管理模块
负责主窗口中所有表格的数据管理
"""

from PySide6.QtWidgets import QTableWidgetItem
from PySide6.QtCore import Qt
import logging
from typing import Optional
from 数据库.数据访问.类_表格列映射管理 import 类_中控表格列, 获取列索引

# 获取当前模块的日志记录器
logger = logging.getLogger(__name__)

class 类_表格数据管理:
    """
    表格数据管理类
    负责处理主窗口中所有表格的数据管理
    """
    
    def __init__(self, 参_主窗口):
        """
        初始化表格数据管理器
        
        参数:
            参_主窗口: 主窗口实例
        """
        self.主窗口 = 参_主窗口
        self.类_模块名 = "表格数据管理"
        
        # 步骤1: 建立字段映射表
        # 使用表格列映射类获取列说明，建立字段名到列索引的映射
        self.字段映射 = {}
        列说明字典 = 类_中控表格列.获取列说明()
        for 索引, 说明 in 列说明字典.items():
            self.字段映射[说明] = 索引
        
        # 步骤2: 建立机器人编号到行索引的缓存
        # 用于快速查找机器人对应的表格行，提高更新性能
        self.机器人行映射 = {}
        
        logger.info("表格数据管理类初始化完成")
    
    def 刷新中控表格数据(self):
        """
        刷新中控表格数据
        从数据库加载所有机器人数据并显示到表格中
        """
        if not self.主窗口.类_数据库管理:
            logger.warning("数据库管理不可用，无法刷新表格数据")
            print("数据库管理不可用，无法刷新表格数据")
            return
        
        # 获取所有机器人数据
        机器人列表 = self.主窗口.类_数据库管理.获取所有机器人()
        
        # 清空表格
        self.主窗口.tableWidget_control.setRowCount(0)
        
        # 添加数据到表格
        for 行索引, 机器人数据 in enumerate(机器人列表):
            self.主窗口.tableWidget_control.insertRow(行索引)
            
            # 使用列映射类中的列索引
            列数据 = [
                str(机器人数据.get('序号', '')),
                str(机器人数据.get('交易对', '')),
                str(机器人数据.get('价格', 0)),
                str(机器人数据.get('预配本金', 0)),
                str(机器人数据.get('预配首单', 0)),
                str(机器人数据.get('本次下单', 0)),
                str(机器人数据.get('复利金额', 0)),
                str(机器人数据.get('累计下单', 0)),
                str(机器人数据.get('订单数量', 0)),
                str(机器人数据.get('最大订单', 0)),
                str(机器人数据.get('浮动收益', 0)),
                str(机器人数据.get('浮动收益率', 0)),
                str(机器人数据.get('结算收益', 0)),
                str(机器人数据.get('持仓均价', 0)),
                str(机器人数据.get('持仓数量', 0)),
                str(机器人数据.get('预估平仓价格', 0)),
                str(机器人数据.get('预估月化率', 0)),
                str(机器人数据.get('预估年化率', 0)),
                str(机器人数据.get('策略状态', '')),
                str(机器人数据.get('当前状态', '')),
                str(机器人数据.get('策略配置', '')),
                str(机器人数据.get('策略类型', '')),
                str(机器人数据.get('子策略', '')),
                str(机器人数据.get('创建时间', '')),
                str(机器人数据.get('机器人编号', ''))
            ]
            
            for 列索引, 数据 in enumerate(列数据):
                self.主窗口.tableWidget_control.setItem(行索引, 列索引, self.创建居中表格项(数据))
        
        logger.debug(f"中控表格数据刷新完成，共加载 {len(机器人列表)} 个机器人")
        print(f"中控表格数据刷新完成，共加载 {len(机器人列表)} 个机器人")
        
        # 步骤4: 刷新机器人行映射缓存
        # 数据刷新完成后，重新建立机器人编号到行索引的映射缓存
        self.刷新机器人行映射缓存()
    
 
    def 创建居中表格项(self, 参_文本: str) -> QTableWidgetItem:
        """
        创建一个居中对齐的表格项
        
        参数:
            参_文本: 表格项显示的文本
            
        返回:
            居中对齐的QTableWidgetItem对象
        """
        项目 = QTableWidgetItem(str(参_文本))
        项目.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        return 项目 
    
    def 更新单个单元格(self, 参_机器人编号: str, 参_字段名: str, 参_新值) -> bool:
        """
        更新单个单元格的数据
        
        @param 参_机器人编号 (str): 要更新的机器人编号
        @param 参_字段名 (str): 要更新的字段名
        @param 参_新值: 新的值
        @return bool: 更新是否成功
        @exception ValueError: 当机器人编号或字段名无效时抛出
        """
        try:
            # 步骤1: 验证输入参数
            # 检查机器人编号和字段名是否有效，确保更新操作的安全性
            if not 参_机器人编号 or not 参_字段名:
                logger.warning(f"[{self.类_模块名}] 机器人编号或字段名为空")
                return False
            
            if 参_字段名 not in self.字段映射:
                logger.warning(f"[{self.类_模块名}] 无效的字段名: {参_字段名}")
                return False
            
            # 步骤2: 查找机器人对应的行索引
            # 使用缓存快速查找，如果缓存中没有则重新扫描表格
            行索引 = self._查找机器人行索引(参_机器人编号)
            if 行索引 is None:
                logger.warning(f"[{self.类_模块名}] 未找到机器人: {参_机器人编号}")
                return False
            
            # 步骤3: 获取列索引并更新单元格
            # 根据字段名获取对应的列索引，然后更新表格中的具体单元格
            列索引 = self.字段映射[参_字段名]
            self.主窗口.tableWidget_control.setItem(行索引, 列索引, self.创建居中表格项(str(参_新值)))
            
            logger.debug(f"[{self.类_模块名}] 成功更新单元格: 机器人{参_机器人编号}, 字段{参_字段名}, 值{参_新值}")
            return True
            
        except Exception as e:
            logger.error(f"[{self.类_模块名}] 更新单个单元格时发生错误: {e}", exc_info=True)
            return False
    
    def 更新多个单元格(self, 参_机器人编号: str, 参_字段字典: dict) -> bool:
        """
        批量更新多个单元格的数据
        
        @param 参_机器人编号 (str): 要更新的机器人编号
        @param 参_字段字典 (dict): 字段名和新值的字典，如 {'浮动收益': 150.5, '策略状态': '运行中'}
        @return bool: 更新是否成功
        @exception ValueError: 当机器人编号无效或字段字典为空时抛出
        """
        try:
            # 步骤1: 验证输入参数
            # 检查机器人编号和字段字典是否有效，确保批量更新的安全性
            if not 参_机器人编号 or not 参_字段字典:
                logger.warning(f"[{self.类_模块名}] 机器人编号或字段字典为空")
                return False
            
            # 步骤2: 查找机器人对应的行索引
            # 使用缓存快速查找，如果缓存中没有则重新扫描表格
            行索引 = self._查找机器人行索引(参_机器人编号)
            if 行索引 is None:
                logger.warning(f"[{self.类_模块名}] 未找到机器人: {参_机器人编号}")
                return False
            
            # 步骤3: 批量更新多个字段
            # 遍历字段字典，逐个更新对应的单元格
            更新成功数 = 0
            for 字段名, 新值 in 参_字段字典.items():
                if 字段名 in self.字段映射:
                    列索引 = self.字段映射[字段名]
                    self.主窗口.tableWidget_control.setItem(行索引, 列索引, self.创建居中表格项(str(新值)))
                    更新成功数 += 1
                else:
                    logger.warning(f"[{self.类_模块名}] 无效的字段名: {字段名}")
            
            logger.debug(f"[{self.类_模块名}] 批量更新完成: 机器人{参_机器人编号}, 成功更新{更新成功数}个字段")
            return 更新成功数 > 0
            
        except Exception as e:
            logger.error(f"[{self.类_模块名}] 更新多个单元格时发生错误: {e}", exc_info=True)
            return False
    
    def _查找机器人行索引(self, 参_机器人编号: str) -> Optional[int]:
        """
        根据机器人编号查找对应的表格行索引
        
        @param 参_机器人编号 (str): 要查找的机器人编号
        @return int: 行索引，如果未找到返回None
        @exception Exception: 查找过程中发生错误时抛出
        """
        try:
            # 步骤1: 检查缓存中是否有该机器人的行索引
            # 如果缓存中有，直接返回，提高查找性能
            if 参_机器人编号 in self.机器人行映射:
                return self.机器人行映射[参_机器人编号]
            
            # 步骤2: 扫描表格查找机器人编号
            # 遍历表格的每一行，查找匹配的机器人编号
            行数 = self.主窗口.tableWidget_control.rowCount()
            机器人编号列索引 = self.字段映射.get('机器人编号', 类_中控表格列.列_机器人编号)
            
            for 行索引 in range(行数):
                机器人编号项 = self.主窗口.tableWidget_control.item(行索引, 机器人编号列索引)
                if 机器人编号项 and 机器人编号项.text() == 参_机器人编号:
                    # 步骤3: 找到后更新缓存并返回行索引
                    # 将找到的行索引存入缓存，提高后续查找性能
                    self.机器人行映射[参_机器人编号] = 行索引
                    return 行索引
            
            # 步骤4: 未找到机器人编号
            # 如果扫描完整个表格都没有找到，返回None
            return None
            
        except Exception as e:
            logger.error(f"[{self.类_模块名}] 查找机器人行索引时发生错误: {e}", exc_info=True)
            return None
    
    def 刷新机器人行映射缓存(self):
        """
        刷新机器人编号到行索引的映射缓存
        当表格数据发生大规模变化时调用此方法重建缓存
        """
        try:
            # 步骤1: 清空现有缓存
            # 删除所有旧的映射关系，准备重新建立
            self.机器人行映射.clear()
            
            # 步骤2: 遍历表格的每一行，重新建立机器人编号到行索引的映射
            行数 = self.主窗口.tableWidget_control.rowCount()
            机器人编号列索引 = self.字段映射.get('机器人编号', 类_中控表格列.列_机器人编号)
            
            for 行索引 in range(行数):
                机器人编号项 = self.主窗口.tableWidget_control.item(行索引, 机器人编号列索引)
                if 机器人编号项 and 机器人编号项.text():
                    self.机器人行映射[机器人编号项.text()] = 行索引
            
            logger.debug(f"[{self.类_模块名}] 机器人行映射缓存刷新完成，共 {len(self.机器人行映射)} 个映射")
            
        except Exception as e:
            logger.error(f"[{self.类_模块名}] 刷新机器人行映射缓存时发生错误: {e}", exc_info=True) 