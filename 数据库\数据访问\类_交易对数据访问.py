"""
交易对数据访问类
负责交易对配置的数据操作
"""

from typing import List, Dict, Any, Optional
import logging
from .类_数据库连接管理 import 类_数据库连接管理

# 获取当前模块的日志记录器
logger = logging.getLogger(__name__)

class 类_交易对数据访问:
    """
    交易对数据访问类
    负责处理交易对配置的数据库操作
    """
    
    def __init__(self, 参_数据库连接管理: Optional[类_数据库连接管理] = None):
        """
        初始化交易对数据访问类
        
        参数:
            参_数据库连接管理: 数据库连接管理实例
        """
        self.类_数据库连接管理 = 参_数据库连接管理 or 类_数据库连接管理()
        logger.info("交易对数据访问类初始化完成")
        print("交易对数据访问类初始化完成")
    
    def 添加交易对配置(self, 参_交易对数据: Dict[str, Any]) -> bool:
        """
        添加交易对配置
        
        参数:
            参_交易对数据: 交易对配置数据字典
            
        返回:
            操作是否成功
        """
        连接 = self.类_数据库连接管理.获取连接()
        游标 = 连接.cursor()
        
        try:
            插入SQL = """
            INSERT OR REPLACE INTO 交易对配置表 
            (交易所, 交易对, 基础货币, 报价货币, 最小下单量, 最大下单量, 
             价格精度, 数量精度, 手续费率, 是否启用) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            游标.execute(插入SQL, (
                参_交易对数据.get('交易所'),
                参_交易对数据.get('交易对'),
                参_交易对数据.get('基础货币'),
                参_交易对数据.get('报价货币'),
                参_交易对数据.get('最小下单量', 0),
                参_交易对数据.get('最大下单量', 999999),
                参_交易对数据.get('价格精度', 8),
                参_交易对数据.get('数量精度', 8),
                参_交易对数据.get('手续费率', 0.001),
                参_交易对数据.get('是否启用', 1)
            ))
            
            连接.commit()
            logger.info(f"添加交易对配置成功: {参_交易对数据.get('交易所')}-{参_交易对数据.get('交易对')}")
            print(f"交易对配置添加成功: {参_交易对数据.get('交易所')}-{参_交易对数据.get('交易对')}")
            return True
            
        except Exception as e:
            连接.rollback()
            logger.error(f"添加交易对配置失败: {e}", exc_info=True)
            print(f"添加交易对配置失败: {e}")
            return False
    
    def 获取交易对列表(self, 参_交易所: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        获取交易对列表
        
        参数:
            参_交易所: 交易所名称，如果为None则获取所有交易所的交易对
            
        返回:
            交易对配置列表
        """
        连接 = self.类_数据库连接管理.获取连接()
        游标 = 连接.cursor()
        
        try:
            if 参_交易所:
                查询SQL = """
                SELECT * FROM 交易对配置表 
                WHERE 交易所 = ? AND 是否启用 = 1
                ORDER BY 交易对
                """
                游标.execute(查询SQL, (参_交易所,))
            else:
                查询SQL = """
                SELECT * FROM 交易对配置表 
                WHERE 是否启用 = 1
                ORDER BY 交易所, 交易对
                """
                游标.execute(查询SQL)
            
            结果 = 游标.fetchall()
            交易对列表 = []
            
            for 行 in 结果:
                交易对数据 = {
                    '序号': 行['序号'],
                    '交易所': 行['交易所'],
                    '交易对': 行['交易对'],
                    '基础货币': 行['基础货币'],
                    '报价货币': 行['报价货币'],
                    '最小下单量': 行['最小下单量'],
                    '最大下单量': 行['最大下单量'],
                    '价格精度': 行['价格精度'],
                    '数量精度': 行['数量精度'],
                    '手续费率': 行['手续费率'],
                    '是否启用': 行['是否启用'],
                    '创建时间': 行['创建时间'],
                    '更新时间': 行['更新时间']
                }
                交易对列表.append(交易对数据)
            
            logger.debug(f"获取交易对列表成功，共 {len(交易对列表)} 条记录")
            return 交易对列表
            
        except Exception as e:
            logger.error(f"获取交易对列表失败: {e}", exc_info=True)
            print(f"获取交易对列表失败: {e}")
            return []
    
    def 初始化默认交易对数据(self):
        """
        初始化默认交易对数据
        为币安和OKX交易所添加BTC、ETH、SOL三个交易对
        """
        # 检查是否已经有数据
        现有数据 = self.获取交易对列表()
        if 现有数据:
            logger.info("交易对配置表已有数据，跳过初始化")
            print("交易对配置表已有数据，跳过初始化")
            return
        
        # 默认交易对数据
        默认交易对列表 = [
            # 币安交易对
            {
                '交易所': '币安',
                '交易对': 'BTC/USDT',
                '基础货币': 'BTC',
                '报价货币': 'USDT',
                '最小下单量': 0.00001,
                '最大下单量': 1000,
                '价格精度': 2,
                '数量精度': 5,
                '手续费率': 0.001,
                '是否启用': 1
            },
            {
                '交易所': '币安',
                '交易对': 'ETH/USDT',
                '基础货币': 'ETH',
                '报价货币': 'USDT',
                '最小下单量': 0.0001,
                '最大下单量': 1000,
                '价格精度': 2,
                '数量精度': 4,
                '手续费率': 0.001,
                '是否启用': 1
            },
            {
                '交易所': '币安',
                '交易对': 'SOL/USDT',
                '基础货币': 'SOL',
                '报价货币': 'USDT',
                '最小下单量': 0.01,
                '最大下单量': 10000,
                '价格精度': 3,
                '数量精度': 2,
                '手续费率': 0.001,
                '是否启用': 1
            },
            # OKX交易对
            {
                '交易所': 'OKX',
                '交易对': 'BTC/USDT',
                '基础货币': 'BTC',
                '报价货币': 'USDT',
                '最小下单量': 0.00001,
                '最大下单量': 1000,
                '价格精度': 2,
                '数量精度': 5,
                '手续费率': 0.001,
                '是否启用': 1
            },
            {
                '交易所': 'OKX',
                '交易对': 'ETH/USDT',
                '基础货币': 'ETH',
                '报价货币': 'USDT',
                '最小下单量': 0.0001,
                '最大下单量': 1000,
                '价格精度': 2,
                '数量精度': 4,
                '手续费率': 0.001,
                '是否启用': 1
            },
            {
                '交易所': 'OKX',
                '交易对': 'SOL/USDT',
                '基础货币': 'SOL',
                '报价货币': 'USDT',
                '最小下单量': 0.01,
                '最大下单量': 10000,
                '价格精度': 3,
                '数量精度': 2,
                '手续费率': 0.001,
                '是否启用': 1
            }
        ]
        
        # 添加默认交易对数据
        成功计数 = 0
        for 交易对数据 in 默认交易对列表:
            if self.添加交易对配置(交易对数据):
                成功计数 += 1
        
        logger.info(f"默认交易对数据初始化完成，成功添加 {成功计数} 个交易对")
        print(f"默认交易对数据初始化完成，成功添加 {成功计数} 个交易对") 