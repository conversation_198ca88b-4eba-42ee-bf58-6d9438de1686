# -*- coding: utf-8 -*-
"""
完整的跨线程UI更新集成测试
使用真实的主窗口和线程管理器进行测试
"""

import sys
import os
import time
import threading

# 添加项目根目录到sys.path
当前目录 = os.path.dirname(os.path.abspath(__file__))
项目根目录 = os.path.dirname(当前目录)
if 项目根目录 not in sys.path:
    sys.path.insert(0, 项目根目录)

def 测试跨线程UI更新():
    """
    测试跨线程UI更新的完整流程
    """
    print("=== 开始完整跨线程UI更新测试 ===")
    
    try:
        # 导入PySide6
        from PySide6.QtWidgets import QApplication
        
        # 导入主窗口
        from 窗口UI布局.UI管理.类_主窗口 import 类_主窗口
        
        # 导入线程管理器
        from 模块类.功能类.类_线程管理 import 类_多线程管理器
        
        print("成功导入所有模块")
        
        # 创建应用
        app = QApplication(sys.argv)
        
        # 创建主窗口
        print("正在创建主窗口...")
        主窗口 = 类_主窗口()
        
        # 检查线程安全UI更新器是否正确初始化
        if hasattr(主窗口, '线程安全UI更新器'):
            print("✅ 线程安全UI更新器已正确初始化")
        else:
            print("❌ 线程安全UI更新器未初始化")
            return
        
        # 检查策略控制管理器是否正确获取UI更新器
        if hasattr(主窗口.策略控制管理, '线程管理器') and 主窗口.策略控制管理.线程管理器:
            if hasattr(主窗口.策略控制管理.线程管理器, 'UI更新器'):
                print("✅ 线程管理器已正确获取UI更新器引用")
            else:
                print("❌ 线程管理器未获取UI更新器引用")
        else:
            print("❌ 线程管理器未正确初始化")
            return
        
        # 显示主窗口
        主窗口.show()
        print("主窗口已显示")
        
        # 创建测试线程来模拟交易数据更新
        def 测试数据更新线程():
            """模拟交易数据更新的测试线程"""
            print("测试数据更新线程开始")
            
            for i in range(5):
                try:
                    # 模拟交易数据
                    机器人编号 = "TEST001"
                    更新数据 = {
                        "本次下单": 100 + (i * 50),
                        "累计下单": (100 + (i * 50)) * (i + 1),
                        "订单数量": i + 1,
                        "持仓均价": 50000 + (i * 100),
                        "持仓数量": 0.001 * (i + 1)
                    }
                    
                    # 通过线程安全UI更新器更新界面
                    主窗口.线程安全UI更新器.请求更新多个单元格(机器人编号, 更新数据)
                    主窗口.线程安全UI更新器.请求显示状态消息(f"第{i+1}次测试更新完成", 2000)
                    
                    print(f"发送第{i+1}次更新请求: {更新数据}")
                    
                    # 等待2秒
                    time.sleep(2)
                    
                except Exception as e:
                    print(f"测试数据更新出错: {e}")
                    break
            
            print("测试数据更新线程结束")
        
        # 启动测试线程
        测试线程 = threading.Thread(target=测试数据更新线程, daemon=True)
        测试线程.start()
        
        print("测试线程已启动，观察UI更新效果...")
        print("程序将在15秒后自动退出")
        
        # 设置定时器自动退出
        from PySide6.QtCore import QTimer
        退出定时器 = QTimer()
        退出定时器.timeout.connect(app.quit)
        退出定时器.start(15000)  # 15秒后退出
        
        # 运行应用
        app.exec()
        
        print("测试完成")
        
    except ImportError as e:
        print(f"导入模块失败: {e}")
        print("请确保所有依赖模块都已正确安装")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def 测试线程管理器集成():
    """
    测试线程管理器与UI更新器的集成
    """
    print("\n=== 测试线程管理器集成 ===")
    
    try:
        # 导入必要模块
        from 窗口UI布局.UI管理.主窗口模块.类_线程安全UI更新器 import 类_线程安全UI更新器
        from 模块类.功能类.类_线程管理 import 类_多线程管理器
        
        # 创建模拟主窗口对象
        class 模拟主窗口:
            def __init__(self):
                self.表格数据管理 = None
                self.类_状态栏管理 = None
        
        模拟窗口 = 模拟主窗口()
        
        # 创建UI更新器
        UI更新器 = 类_线程安全UI更新器(模拟窗口)
        print("✅ UI更新器创建成功")
        
        # 创建线程管理器并传入UI更新器
        线程管理器 = 类_多线程管理器(参_UI更新器=UI更新器)
        print("✅ 线程管理器创建成功")
        
        # 检查UI更新器是否正确传递
        if hasattr(线程管理器, 'UI更新器') and 线程管理器.UI更新器 is UI更新器:
            print("✅ UI更新器引用传递正确")
        else:
            print("❌ UI更新器引用传递失败")
        
        print("线程管理器集成测试完成")
        
    except Exception as e:
        print(f"线程管理器集成测试失败: {e}")
        import traceback
        traceback.print_exc()

def 运行所有测试():
    """运行所有测试"""
    print("开始运行跨线程UI更新的所有测试")
    
    # 测试1: 线程管理器集成测试
    测试线程管理器集成()
    
    # 测试2: 完整UI更新测试
    测试跨线程UI更新()
    
    print("\n所有测试完成")

if __name__ == "__main__":
    运行所有测试()
