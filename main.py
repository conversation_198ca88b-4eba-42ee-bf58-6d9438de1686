#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
量化交易系统主入口
"""

import os
import sys
import logging
from logging.handlers import TimedRotatingFileHandler

def 配置日志系统(日志级别=logging.INFO):
    """
    配置标准Python日志系统
    
    参数:
        日志级别: 日志记录的级别，默认为INFO
                可选值：
                - logging.DEBUG (10): 调试信息
                - logging.INFO (20): 一般信息
                - logging.WARNING (30): 警告信息
                - logging.ERROR (40): 错误信息
                - logging.CRITICAL (50): 严重错误
    """
    # 创建logs目录
    if not os.path.exists("logs"):
        os.makedirs("logs")
    
    # 配置根日志记录器
    根日志记录器 = logging.getLogger()
    根日志记录器.setLevel(日志级别)
    
    # 清除现有的处理器
    for 处理器 in 根日志记录器.handlers[:]:
        根日志记录器.removeHandler(处理器)
    
    # 创建格式化器
    格式化器 = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # 添加按日期轮转的处理器（每天轮转，保留30天）
    日志文件 = os.path.join("logs", "系统日志.log")
    日期处理器 = TimedRotatingFileHandler(
        日志文件 + '.daily',
        when='midnight',
        interval=1,
        backupCount=30,
        encoding='utf-8'
    )
    日期处理器.setFormatter(格式化器)
    根日志记录器.addHandler(日期处理器)
    
    # 添加控制台处理器
    控制台处理器 = logging.StreamHandler()
    控制台处理器.setFormatter(格式化器)
    根日志记录器.addHandler(控制台处理器)
    
    logging.info("日志系统初始化完成")

def main():
    """
    主函数
    """
    # 配置日志系统 - 设置为ERROR级别，只记录错误和严重错误
    配置日志系统(日志级别=logging.WARNING)
    
    # 导入其他模块
    try:
        # 这里导入其他模块
        from 窗口UI布局.UI管理.类_主窗口 import 类_主窗口
        from PySide6.QtWidgets import QApplication
        
        logging.info("开始初始化应用程序")
        
        # 创建应用程序
        应用程序 = QApplication(sys.argv)
        
        # 创建主窗口
        主窗口 = 类_主窗口()
        
        # 显示主窗口
        主窗口.show()
        
        # 运行应用程序
        sys.exit(应用程序.exec())
        
    except Exception as e:
        logging.error(f"程序启动失败: {e}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    main() 