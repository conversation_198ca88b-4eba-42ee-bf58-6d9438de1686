# -*- coding: utf-8 -*-
"""
诊断UI更新器传递问题
检查UI更新器是否正确传递到各个组件
"""

import sys
import os

# 添加项目根目录到sys.path
当前目录 = os.path.dirname(os.path.abspath(__file__))
项目根目录 = os.path.dirname(当前目录)
if 项目根目录 not in sys.path:
    sys.path.insert(0, 项目根目录)

def 诊断UI更新器传递():
    """诊断UI更新器在各个组件中的传递情况"""
    print("=== 开始诊断UI更新器传递问题 ===")
    
    try:
        # 步骤1: 测试主窗口中的UI更新器初始化
        print("\n1. 测试主窗口UI更新器初始化...")
        
        from PySide6.QtWidgets import QApplication
        app = QApplication(sys.argv)
        
        from 窗口UI布局.UI管理.类_主窗口 import 类_主窗口
        主窗口 = 类_主窗口()
        
        if hasattr(主窗口, '线程安全UI更新器'):
            print("✅ 主窗口.线程安全UI更新器 存在")
            print(f"   类型: {type(主窗口.线程安全UI更新器)}")
        else:
            print("❌ 主窗口.线程安全UI更新器 不存在")
            return False
        
        # 步骤2: 测试策略控制管理器中的线程管理器
        print("\n2. 测试策略控制管理器...")
        
        if hasattr(主窗口, '策略控制管理'):
            print("✅ 主窗口.策略控制管理 存在")
            
            if hasattr(主窗口.策略控制管理, '线程管理器'):
                print("✅ 策略控制管理.线程管理器 存在")
                
                if 主窗口.策略控制管理.线程管理器:
                    print("✅ 线程管理器不为None")
                    
                    if hasattr(主窗口.策略控制管理.线程管理器, 'UI更新器'):
                        print("✅ 线程管理器.UI更新器 属性存在")
                        
                        if 主窗口.策略控制管理.线程管理器.UI更新器:
                            print("✅ 线程管理器.UI更新器 不为None")
                            print(f"   类型: {type(主窗口.策略控制管理.线程管理器.UI更新器)}")
                            
                            # 检查是否是同一个对象
                            if 主窗口.策略控制管理.线程管理器.UI更新器 is 主窗口.线程安全UI更新器:
                                print("✅ UI更新器引用传递正确")
                            else:
                                print("❌ UI更新器引用不匹配")
                                return False
                        else:
                            print("❌ 线程管理器.UI更新器 为None")
                            return False
                    else:
                        print("❌ 线程管理器.UI更新器 属性不存在")
                        return False
                else:
                    print("❌ 线程管理器为None")
                    return False
            else:
                print("❌ 策略控制管理.线程管理器 不存在")
                return False
        else:
            print("❌ 主窗口.策略控制管理 不存在")
            return False
        
        # 步骤3: 测试核心计算类的UI更新器接收
        print("\n3. 测试核心计算类...")
        
        from 模块类.功能类.类_核心计算 import 类_核心计算
        
        # 测试不传递UI更新器的情况
        核心计算1 = 类_核心计算()
        if hasattr(核心计算1, '类_UI更新器'):
            if 核心计算1.类_UI更新器 is None:
                print("✅ 核心计算类默认UI更新器为None（正常）")
            else:
                print(f"⚠️ 核心计算类默认UI更新器不为None: {核心计算1.类_UI更新器}")
        else:
            print("❌ 核心计算类没有类_UI更新器属性")
            return False
        
        # 测试传递UI更新器的情况
        核心计算2 = 类_核心计算(参_UI更新器=主窗口.线程安全UI更新器)
        if 核心计算2.类_UI更新器 is 主窗口.线程安全UI更新器:
            print("✅ 核心计算类正确接收UI更新器")
        else:
            print("❌ 核心计算类未正确接收UI更新器")
            return False
        
        # 步骤4: 测试多线程主程序的参数传递
        print("\n4. 测试多线程主程序参数...")
        
        from 模块类.功能类.类_线程管理 import 多线程主程序
        import threading
        import inspect
        
        # 检查函数签名
        签名 = inspect.signature(多线程主程序)
        参数列表 = list(签名.parameters.keys())
        print(f"多线程主程序参数: {参数列表}")
        
        if 'UI更新器' in 参数列表:
            print("✅ 多线程主程序包含UI更新器参数")
        else:
            print("❌ 多线程主程序缺少UI更新器参数")
            return False
        
        print("\n=== 诊断完成 ===")
        print("✅ 所有组件的UI更新器传递都正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 诊断过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def 测试实际启动流程():
    """测试实际的线程启动流程"""
    print("\n=== 测试实际启动流程 ===")
    
    try:
        from PySide6.QtWidgets import QApplication
        app = QApplication(sys.argv)
        
        from 窗口UI布局.UI管理.类_主窗口 import 类_主窗口
        主窗口 = 类_主窗口()
        
        # 模拟启动一个线程
        if hasattr(主窗口, '策略控制管理') and 主窗口.策略控制管理.线程管理器:
            print("尝试启动测试线程...")
            
            # 这里不实际启动线程，只是检查参数传递
            线程管理器 = 主窗口.策略控制管理.线程管理器
            
            print(f"线程管理器类型: {type(线程管理器)}")
            print(f"线程管理器UI更新器: {线程管理器.UI更新器}")
            
            if 线程管理器.UI更新器:
                print("✅ 线程管理器中的UI更新器可用")
                
                # 测试UI更新器的方法
                try:
                    线程管理器.UI更新器.请求显示状态消息("测试消息", 1000)
                    print("✅ UI更新器方法调用成功")
                except Exception as e:
                    print(f"❌ UI更新器方法调用失败: {e}")
            else:
                print("❌ 线程管理器中的UI更新器不可用")
        
    except Exception as e:
        print(f"测试启动流程失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 运行诊断
    成功 = 诊断UI更新器传递()
    
    if 成功:
        测试实际启动流程()
    else:
        print("\n请根据上述诊断结果修复问题后再次测试")
