<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="enabled">
   <bool>true</bool>
  </property>
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1440</width>
    <height>770</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>现货量化软件</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout_centralwidget">
    <item>
     <widget class="QTabWidget" name="tabWidget_main">
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
      <property name="currentIndex">
       <number>0</number>
      </property>
      <widget class="QWidget" name="tab_Control">
       <attribute name="title">
        <string>中控</string>
       </attribute>
       <layout class="QHBoxLayout" name="horizontalLayout_Control">
        <item>
         <widget class="QTableWidget" name="tableWidget_control">
          <property name="enabled">
           <bool>true</bool>
          </property>
          <property name="editTriggers">
           <set>QAbstractItemView::EditTrigger::NoEditTriggers</set>
          </property>
          <property name="selectionBehavior">
           <enum>QAbstractItemView::SelectionBehavior::SelectRows</enum>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tab_Order_History">
       <attribute name="title">
        <string>记录</string>
       </attribute>
       <layout class="QHBoxLayout" name="horizontalLayout_Order_History">
        <item>
         <widget class="QGroupBox" name="groupBox_Open_Orders">
          <property name="title">
           <string>持仓</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_Open_Orders">
           <item>
            <widget class="QTableWidget" name="tableWidget_Open_Orders">
             <property name="editTriggers">
              <set>QAbstractItemView::EditTrigger::NoEditTriggers</set>
             </property>
             <property name="selectionBehavior">
              <enum>QAbstractItemView::SelectionBehavior::SelectRows</enum>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBox_Closing_Orders">
          <property name="title">
           <string>平仓</string>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayout_Closing_Orders">
           <item>
            <widget class="QTableWidget" name="tableWidget_Closing_Orders">
             <property name="editTriggers">
              <set>QAbstractItemView::EditTrigger::NoEditTriggers</set>
             </property>
             <property name="selectionBehavior">
              <enum>QAbstractItemView::SelectionBehavior::SelectRows</enum>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tab_Visualization">
       <attribute name="title">
        <string>可视化</string>
       </attribute>
       <layout class="QHBoxLayout" name="horizontalLayout_Visualization">
        <item>
         <widget class="QGroupBox" name="groupBox_Chart">
          <property name="title">
           <string>图表</string>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayout_Chart">
           <item>
            <widget class="QGraphicsView" name="graphicsView"/>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBox_Historical_orders">
          <property name="title">
           <string>历史数据</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_Historical_orders">
           <item>
            <widget class="QTableWidget" name="tableWidget_Historical_orders">
             <property name="editTriggers">
              <set>QAbstractItemView::EditTrigger::NoEditTriggers</set>
             </property>
             <property name="selectionBehavior">
              <enum>QAbstractItemView::SelectionBehavior::SelectRows</enum>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
    <item>
     <widget class="QTabWidget" name="tabWidget_Configuration">
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>200</height>
       </size>
      </property>
      <property name="currentIndex">
       <number>1</number>
      </property>
      <widget class="QWidget" name="tab_Config">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <attribute name="title">
        <string>配置</string>
       </attribute>
       <layout class="QHBoxLayout" name="horizontalLayout_Configuration">
        <item>
         <widget class="QGroupBox" name="groupBox_Global_Control">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="maximumSize">
           <size>
            <width>200</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="title">
           <string>控制</string>
          </property>
          <layout class="QGridLayout" name="gridLayout_Global_Control">
           <item row="0" column="0">
            <widget class="QPushButton" name="pushButton_All_Start">
             <property name="text">
              <string>全部开始</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QPushButton" name="pushButton_All_Stop">
             <property name="text">
              <string>全部停止</string>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QPushButton" name="pushButton_All_Pause_Order">
             <property name="text">
              <string>全部暂停补单</string>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QPushButton" name="pushButton_All_Recovery_Order">
             <property name="text">
              <string>全部恢复补单</string>
             </property>
            </widget>
           </item>
           <item row="4" column="0">
            <widget class="QPushButton" name="pushButton_All_Sell">
             <property name="text">
              <string>一键平仓</string>
             </property>
            </widget>
           </item>
           <item row="4" column="1">
            <widget class="QPushButton" name="pushButton_All_Clear">
             <property name="text">
              <string>清空全部</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBox_exchanges">
          <property name="maximumSize">
           <size>
            <width>100</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="title">
           <string>交易所</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_exchanges">
           <item>
            <widget class="QRadioButton" name="radioButton_Binance">
             <property name="font">
              <font>
               <pointsize>12</pointsize>
              </font>
             </property>
             <property name="text">
              <string>币安</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QRadioButton" name="radioButton_OKX">
             <property name="font">
              <font>
               <pointsize>12</pointsize>
              </font>
             </property>
             <property name="text">
              <string>OKX</string>
             </property>
             <property name="checked">
              <bool>true</bool>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBox_order_mode">
          <property name="maximumSize">
           <size>
            <width>100</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="title">
           <string>下单模式</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_order_mode">
           <item>
            <widget class="QRadioButton" name="radioButton_Firm_Offer">
             <property name="text">
              <string>实盘下单</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QRadioButton" name="radioButton_Online_Simulation">
             <property name="text">
              <string>线上模拟</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QRadioButton" name="radioButton_Local_simulation">
             <property name="text">
              <string>本地模拟</string>
             </property>
             <property name="checked">
              <bool>true</bool>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBox_OpenAndClose_log">
          <property name="title">
           <string>开平仓日志</string>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayout">
           <item>
            <widget class="QTableWidget" name="tableWidget_OpenAndClose_log">
             <property name="editTriggers">
              <set>QAbstractItemView::EditTrigger::NoEditTriggers</set>
             </property>
             <property name="selectionBehavior">
              <enum>QAbstractItemView::SelectionBehavior::SelectRows</enum>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tab_API">
       <attribute name="title">
        <string>API配置</string>
       </attribute>
       <layout class="QHBoxLayout" name="horizontalLayout_API">
        <item>
         <widget class="QGroupBox" name="groupBox_Binance_API">
          <property name="title">
           <string>币安</string>
          </property>
          <layout class="QGridLayout" name="gridLayout_Binance">
           <item row="0" column="1">
            <widget class="QLineEdit" name="lineEdit_Binance_SKEY">
             <property name="echoMode">
              <enum>QLineEdit::EchoMode::Normal</enum>
             </property>
            </widget>
           </item>
           <item row="0" column="0">
            <widget class="QLabel" name="label_Binance_SKEY">
             <property name="text">
              <string>S_Key:</string>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QLineEdit" name="lineEdit_Binance_AKEY">
             <property name="echoMode">
              <enum>QLineEdit::EchoMode::Password</enum>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="label_Binance_AKEY">
             <property name="text">
              <string>A_Key:</string>
             </property>
            </widget>
           </item>
           <item row="2" column="1">
            <widget class="QPushButton" name="pushButton_Binance_save">
             <property name="text">
              <string>保存</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBox_OKX_API">
          <property name="title">
           <string>OKX</string>
          </property>
          <layout class="QGridLayout" name="gridLayout_OKX">
           <item row="2" column="0">
            <widget class="QLabel" name="label_OKX_PASS">
             <property name="text">
              <string>Pass:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="0" column="0">
            <widget class="QLabel" name="label_OKX_SKEY">
             <property name="text">
              <string>S_Key:</string>
             </property>
            </widget>
           </item>
           <item row="2" column="2">
            <widget class="QLineEdit" name="lineEdit_OKX_PASS">
             <property name="echoMode">
              <enum>QLineEdit::EchoMode::Password</enum>
             </property>
            </widget>
           </item>
           <item row="0" column="2">
            <widget class="QLineEdit" name="lineEdit_OKX_SKEY">
             <property name="echoMode">
              <enum>QLineEdit::EchoMode::Normal</enum>
             </property>
            </widget>
           </item>
           <item row="1" column="2">
            <widget class="QLineEdit" name="lineEdit_OKX_AKEY">
             <property name="echoMode">
              <enum>QLineEdit::EchoMode::Password</enum>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="label_OKX_AKEY">
             <property name="text">
              <string>A_Key:</string>
             </property>
            </widget>
           </item>
           <item row="3" column="2">
            <widget class="QPushButton" name="pushButton_OKX_save">
             <property name="text">
              <string>保存</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QStatusBar" name="statusbar">
   <property name="sizeGripEnabled">
    <bool>true</bool>
   </property>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1440</width>
     <height>33</height>
    </rect>
   </property>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
