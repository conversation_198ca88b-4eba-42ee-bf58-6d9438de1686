# -*- coding: utf-8 -*-
"""
线程安全UI更新器模块
负责处理来自工作线程的UI更新请求，确保线程安全
"""

from PySide6.QtCore import QObject, Signal, Slot
import logging
from typing import Dict, Any, Optional

# 获取当前模块的日志记录器
logger = logging.getLogger(__name__)

class 类_线程安全UI更新器(QObject):
    """
    线程安全UI更新器类
    使用信号槽机制实现跨线程的UI更新
    """
    
    # 定义信号，用于跨线程通信
    信号_更新单个单元格 = Signal(str, str, object)  # 机器人编号, 字段名, 新值
    信号_更新多个单元格 = Signal(str, dict)  # 机器人编号, 字段字典
    信号_刷新表格数据 = Signal()  # 刷新整个表格
    信号_显示状态消息 = Signal(str, int)  # 消息内容, 超时时间(毫秒)
    
    def __init__(self, 参_主窗口):
        """
        初始化线程安全UI更新器
        
        @param 参_主窗口: 主窗口实例
        @return None
        @exception Exception: 初始化过程中可能发生的未知错误
        """
        super().__init__()
        
        # 步骤1: 保存主窗口引用
        # 将传入的主窗口实例保存到实例变量中，供后续方法使用
        self.主窗口 = 参_主窗口
        self.类_模块名 = "线程安全UI更新器"
        
        # 步骤2: 连接信号槽
        # 将信号连接到对应的UI更新方法，确保在主线程中执行
        self.连接信号槽()
        
        logger.info("线程安全UI更新器初始化完成")
    
    def 连接信号槽(self):
        """
        连接所有信号槽
        将信号连接到对应的UI更新方法
        
        @return None
        @exception Exception: 连接过程中可能发生的未知错误
        """
        try:
            # 步骤1: 连接单元格更新信号
            # 将更新单元格的信号连接到对应的槽函数
            self.信号_更新单个单元格.connect(self.槽_更新单个单元格)
            self.信号_更新多个单元格.connect(self.槽_更新多个单元格)
            
            # 步骤2: 连接表格刷新信号
            # 将刷新表格的信号连接到对应的槽函数
            self.信号_刷新表格数据.connect(self.槽_刷新表格数据)
            
            # 步骤3: 连接状态消息信号
            # 将显示状态消息的信号连接到对应的槽函数
            self.信号_显示状态消息.connect(self.槽_显示状态消息)
            
            logger.debug(f"[{self.类_模块名}] 信号槽连接完成")
            
        except Exception as e:
            logger.error(f"[{self.类_模块名}] 连接信号槽时发生错误: {e}", exc_info=True)
    
    # ========== 线程安全的公共接口方法 ==========
    # 这些方法可以在任何线程中安全调用
    
    def 请求更新单个单元格(self, 参_机器人编号: str, 参_字段名: str, 参_新值) -> None:
        """
        请求更新单个单元格（线程安全）
        
        @param 参_机器人编号 (str): 要更新的机器人编号
        @param 参_字段名 (str): 要更新的字段名
        @param 参_新值: 新的值
        @return None
        @exception Exception: 发送信号过程中可能发生的未知错误
        """
        try:
            # 步骤1: 验证输入参数
            # 检查机器人编号和字段名是否有效
            if not 参_机器人编号 or not 参_字段名:
                logger.warning(f"[{self.类_模块名}] 机器人编号或字段名为空")
                return
            
            # 步骤2: 发送更新信号
            # 通过信号槽机制将更新请求发送到主线程
            self.信号_更新单个单元格.emit(参_机器人编号, 参_字段名, 参_新值)
            
            logger.debug(f"[{self.类_模块名}] 发送单元格更新请求: 机器人{参_机器人编号}, 字段{参_字段名}")
            
        except Exception as e:
            logger.error(f"[{self.类_模块名}] 请求更新单个单元格时发生错误: {e}", exc_info=True)
    
    def 请求更新多个单元格(self, 参_机器人编号: str, 参_字段字典: dict) -> None:
        """
        请求批量更新多个单元格（线程安全）
        
        @param 参_机器人编号 (str): 要更新的机器人编号
        @param 参_字段字典 (dict): 字段名和新值的字典
        @return None
        @exception Exception: 发送信号过程中可能发生的未知错误
        """
        try:
            # 步骤1: 验证输入参数
            # 检查机器人编号和字段字典是否有效
            if not 参_机器人编号 or not 参_字段字典:
                logger.warning(f"[{self.类_模块名}] 机器人编号或字段字典为空")
                return
            
            # 步骤2: 发送更新信号
            # 通过信号槽机制将批量更新请求发送到主线程
            self.信号_更新多个单元格.emit(参_机器人编号, 参_字段字典)
            
            logger.debug(f"[{self.类_模块名}] 发送多单元格更新请求: 机器人{参_机器人编号}, 字段数{len(参_字段字典)}")
            
        except Exception as e:
            logger.error(f"[{self.类_模块名}] 请求更新多个单元格时发生错误: {e}", exc_info=True)
    
    def 请求刷新表格数据(self) -> None:
        """
        请求刷新整个表格数据（线程安全）
        
        @return None
        @exception Exception: 发送信号过程中可能发生的未知错误
        """
        try:
            # 发送刷新表格信号
            self.信号_刷新表格数据.emit()
            logger.debug(f"[{self.类_模块名}] 发送表格刷新请求")
            
        except Exception as e:
            logger.error(f"[{self.类_模块名}] 请求刷新表格数据时发生错误: {e}", exc_info=True)
    
    def 请求显示状态消息(self, 参_消息: str, 参_超时毫秒: int = 5000) -> None:
        """
        请求显示状态栏消息（线程安全）
        
        @param 参_消息 (str): 要显示的消息内容
        @param 参_超时毫秒 (int): 消息显示时间，默认5秒
        @return None
        @exception Exception: 发送信号过程中可能发生的未知错误
        """
        try:
            # 发送状态消息信号
            self.信号_显示状态消息.emit(参_消息, 参_超时毫秒)
            logger.debug(f"[{self.类_模块名}] 发送状态消息请求: {参_消息}")
            
        except Exception as e:
            logger.error(f"[{self.类_模块名}] 请求显示状态消息时发生错误: {e}", exc_info=True)
    
    # ========== 槽函数（在主线程中执行） ==========
    
    @Slot(str, str, object)
    def 槽_更新单个单元格(self, 参_机器人编号: str, 参_字段名: str, 参_新值) -> None:
        """
        更新单个单元格的槽函数（在主线程中执行）
        
        @param 参_机器人编号 (str): 要更新的机器人编号
        @param 参_字段名 (str): 要更新的字段名
        @param 参_新值: 新的值
        @return None
        @exception Exception: 更新过程中可能发生的未知错误
        """
        try:
            # 调用表格数据管理器的更新方法
            if hasattr(self.主窗口, '表格数据管理'):
                成功 = self.主窗口.表格数据管理.更新单个单元格(参_机器人编号, 参_字段名, 参_新值)
                if 成功:
                    logger.debug(f"[{self.类_模块名}] 成功更新单元格: 机器人{参_机器人编号}, 字段{参_字段名}")
                else:
                    logger.warning(f"[{self.类_模块名}] 更新单元格失败: 机器人{参_机器人编号}, 字段{参_字段名}")
            else:
                logger.error(f"[{self.类_模块名}] 主窗口缺少表格数据管理器")
                
        except Exception as e:
            logger.error(f"[{self.类_模块名}] 槽_更新单个单元格时发生错误: {e}", exc_info=True)
    
    @Slot(str, dict)
    def 槽_更新多个单元格(self, 参_机器人编号: str, 参_字段字典: dict) -> None:
        """
        批量更新多个单元格的槽函数（在主线程中执行）
        
        @param 参_机器人编号 (str): 要更新的机器人编号
        @param 参_字段字典 (dict): 字段名和新值的字典
        @return None
        @exception Exception: 更新过程中可能发生的未知错误
        """
        try:
            # 调用表格数据管理器的批量更新方法
            if hasattr(self.主窗口, '表格数据管理'):
                成功 = self.主窗口.表格数据管理.更新多个单元格(参_机器人编号, 参_字段字典)
                if 成功:
                    logger.debug(f"[{self.类_模块名}] 成功批量更新单元格: 机器人{参_机器人编号}, 字段数{len(参_字段字典)}")
                else:
                    logger.warning(f"[{self.类_模块名}] 批量更新单元格失败: 机器人{参_机器人编号}")
            else:
                logger.error(f"[{self.类_模块名}] 主窗口缺少表格数据管理器")
                
        except Exception as e:
            logger.error(f"[{self.类_模块名}] 槽_更新多个单元格时发生错误: {e}", exc_info=True)
    
    @Slot()
    def 槽_刷新表格数据(self) -> None:
        """
        刷新表格数据的槽函数（在主线程中执行）
        
        @return None
        @exception Exception: 刷新过程中可能发生的未知错误
        """
        try:
            # 调用表格数据管理器的刷新方法
            if hasattr(self.主窗口, '表格数据管理'):
                self.主窗口.表格数据管理.刷新中控表格数据()
                logger.debug(f"[{self.类_模块名}] 成功刷新表格数据")
            else:
                logger.error(f"[{self.类_模块名}] 主窗口缺少表格数据管理器")
                
        except Exception as e:
            logger.error(f"[{self.类_模块名}] 槽_刷新表格数据时发生错误: {e}", exc_info=True)
    
    @Slot(str, int)
    def 槽_显示状态消息(self, 参_消息: str, 参_超时毫秒: int) -> None:
        """
        显示状态栏消息的槽函数（在主线程中执行）
        
        @param 参_消息 (str): 要显示的消息内容
        @param 参_超时毫秒 (int): 消息显示时间
        @return None
        @exception Exception: 显示过程中可能发生的未知错误
        """
        try:
            # 调用状态栏管理器的显示方法
            if hasattr(self.主窗口, '类_状态栏管理'):
                self.主窗口.类_状态栏管理.显示系统提示(参_消息, 参_超时毫秒)
                logger.debug(f"[{self.类_模块名}] 成功显示状态消息: {参_消息}")
            else:
                logger.error(f"[{self.类_模块名}] 主窗口缺少状态栏管理器")
                
        except Exception as e:
            logger.error(f"[{self.类_模块名}] 槽_显示状态消息时发生错误: {e}", exc_info=True)
