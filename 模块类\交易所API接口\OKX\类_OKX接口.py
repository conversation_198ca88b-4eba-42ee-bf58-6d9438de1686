# -*- coding: utf-8 -*-
"""
简单OKX交易所类
只实现基本的蜡烛数据获取功能
适合新手学习使用
"""

import requests
import json
import time
from typing import List, Dict, Any, Optional
import logging

# 获取当前模块的日志记录器
logger = logging.getLogger(__name__)


class 类_OKX:
    """
    简单OKX交易所类
    只实现蜡烛数据获取功能，去掉复杂的架构
    """
    
    def __init__(self):
        """
        初始化OKX交易所

        @return None
        @exception Exception: 初始化过程中可能发生的未知错误
        """
        # 步骤1: 初始化基础URL
        # 设置OKX交易所的基础URL，用于构建API请求地址
        self.类_基础URL = "https://www.okx.com"
        
        # 步骤2: 初始化请求头
        # 设置HTTP请求头，包含内容类型和用户代理信息
        # 这些信息用于模拟浏览器请求，避免被服务器拒绝
        self.类_请求头 = {
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
        
        # 步骤3: 记录初始化完成
        # 记录OKX交易所初始化完成的信息
        print("OKX交易所初始化完成")
        logger.info("OKX交易所初始化完成")
    
    def 获取蜡烛数据(self, 参_交易对: str, 参_时间周期: str = "1m", 参_数量: int = 100) -> List[Dict[str, Any]]:
        """
        获取蜡烛数据（K线数据）
        
        @param 参_交易对 (str): 交易对名称，如 "BTC/USDT"
        @param 参_时间周期 (str): 时间周期，如 "1m", "5m", "1H", "1D" 等
        @param 参_数量 (int): 返回K线数量，最大300
            
        @return List[Dict[str, Any]]: 蜡烛数据列表，每个元素包含：
            {
                "时间戳": "1640995200000",
                "开盘价": "46200.1",
                "最高价": "46200.1", 
                "最低价": "46100.1",
                "收盘价": "46100.1",
                "成交量": "0.1",
                "成交额": "4610.01"
            }
        @exception requests.exceptions.Timeout: 请求超时时抛出
        @exception requests.exceptions.RequestException: 网络请求失败时抛出
        @exception Exception: 其他未知错误时抛出
        """
        try:
            # 步骤1: 记录开始执行
            # 记录开始获取蜡烛数据的信息，包含交易对、时间周期和数量
            print(f"开始获取蜡烛数据: {参_交易对}, 周期: {参_时间周期}, 数量: {参_数量}")
            logger.info(f"开始获取蜡烛数据: {参_交易对}, 周期: {参_时间周期}, 数量: {参_数量}")
            
            # 步骤1.5: 转换交易对格式
            # 将数据库格式的交易对转换为OKX API格式
            API格式交易对 = self.转换交易对格式(参_交易对, "OKX")
            
            # 步骤2: 构建请求参数
            # 构建API请求所需的参数
            # 包括交易对ID、时间周期和数量限制
            请求路径 = "/api/v5/market/candles"
            请求参数 = {
                "instId": API格式交易对,
                "bar": 参_时间周期,
                "limit": str(min(参_数量, 300))  # OKX最大限制300
            }
            
            # 步骤3: 发送GET请求
            # 构建完整的URL并发送HTTP GET请求
            # 设置10秒超时时间，避免请求长时间等待
            完整URL = f"{self.类_基础URL}{请求路径}"
            响应 = requests.get(完整URL, params=请求参数, headers=self.类_请求头, timeout=10)
            
            # 步骤4: 检查响应状态
            # 检查HTTP响应状态码，如果不是200则表示请求失败
            if 响应.status_code != 200:
                print(f"请求失败，状态码: {响应.status_code}")
                logger.error(f"请求失败，状态码: {响应.status_code}")
                return []
            
            # 步骤5: 解析响应数据
            # 将JSON响应转换为Python字典对象
            响应数据 = 响应.json()
            
            # 步骤6: 检查API响应是否成功
            # 检查OKX API返回的业务状态码，如果不是"0"则表示API调用失败
            if 响应数据.get("code") != "0":
                错误信息 = 响应数据.get("msg", "未知错误")
                print(f"API返回错误: {错误信息}")
                logger.error(f"API返回错误: {错误信息}")
                return []
            
            # 步骤7: 解析蜡烛数据
            # 从响应数据中提取蜡烛数据列表
            # 逐个解析每条蜡烛数据，转换为标准格式
            蜡烛数据列表 = []
            原始数据列表 = 响应数据.get("data", [])
            
            for 原始数据 in 原始数据列表:
                try:
                    解析后的数据 = self._解析蜡烛数据(原始数据)
                    蜡烛数据列表.append(解析后的数据)
                except Exception as e:
                    print(f"解析蜡烛数据失败: {e}")
                    logger.warning(f"解析蜡烛数据失败: {e}")
                    continue
            
            # 步骤8: 记录成功信息
            # 记录成功获取蜡烛数据的数量和日志
            print(f"成功获取 {len(蜡烛数据列表)} 条蜡烛数据")
            logger.info(f"成功获取 {len(蜡烛数据列表)} 条蜡烛数据")
            
            return 蜡烛数据列表
            
        except requests.exceptions.Timeout:
            # 步骤9: 处理超时异常
            # 当请求超时时，记录错误信息并返回空列表
            错误信息 = "请求超时"
            print(错误信息)
            logger.error(错误信息)
            return []
            
        except requests.exceptions.RequestException as e:
            # 步骤10: 处理网络请求异常
            # 当网络请求失败时，记录错误信息并返回空列表
            错误信息 = f"网络请求失败: {e}"
            print(错误信息)
            logger.error(错误信息)
            return []
            
        except Exception as e:
            # 步骤11: 处理其他未知异常
            # 当发生其他未知错误时，记录详细错误信息并返回空列表
            错误信息 = f"获取蜡烛数据失败: {e}"
            print(错误信息)
            logger.error(错误信息, exc_info=True)
            return []
    
    def 批量获取蜡烛数据(self, 参_交易对: str, 参_时间周期列表: List[str], 参_数量: int = 100) -> Dict[str, List[Dict[str, Any]]]:
        """
        批量获取多个时间周期的蜡烛数据
        
        @param 参_交易对 (str): 交易对名称，如 "BTC/USDT"
        @param 参_时间周期列表 (List[str]): 时间周期列表，如 ["1m", "5m", "15m", "1H"]
        @param 参_数量 (int): 每个周期返回的K线数量，最大300
            
        @return Dict[str, List[Dict[str, Any]]]: 不同周期的蜡烛数据字典，格式为:
            {
                "1m": [蜡烛数据列表],
                "5m": [蜡烛数据列表],
                ...
            }
        @exception Exception: 批量获取过程中可能发生的未知错误
        """
        try:
            # 步骤1: 记录开始批量获取
            # 记录开始批量获取蜡烛数据的信息，包含交易对、时间周期列表和数量
            print(f"开始批量获取蜡烛数据: {参_交易对}, 周期列表: {参_时间周期列表}, 数量: {参_数量}")
            logger.info(f"开始批量获取蜡烛数据: {参_交易对}, 周期列表: {参_时间周期列表}, 数量: {参_数量}")
            
            # 步骤1.5: 转换交易对格式
            # 将数据库格式的交易对转换为OKX API格式
            API格式交易对 = self.转换交易对格式(参_交易对, "OKX")
            
            # 步骤2: 初始化结果字典
            # 创建一个字典用于存储不同周期的蜡烛数据
            # 键为时间周期，值为对应的蜡烛数据列表
            结果字典 = {}
            
            # 步骤3: 循环获取每个周期的数据
            # 遍历时间周期列表，逐个获取每个周期的蜡烛数据
            # 将获取到的数据存储到结果字典中
            for 时间周期 in 参_时间周期列表:
                # 调用已有的获取蜡烛数据方法
                蜡烛数据 = self.获取蜡烛数据(参_交易对, 时间周期, 参_数量)
                # 将获取到的数据存入结果字典
                结果字典[时间周期] = 蜡烛数据
                
                # 添加短暂延迟，避免API请求过于频繁
                time.sleep(0.01)
            
            # 步骤4: 记录批量获取完成
            # 记录批量获取蜡烛数据完成的信息，包含获取到的周期数量
            print(f"批量获取完成，共 {len(结果字典)} 个周期")
            logger.info(f"批量获取完成，共 {len(结果字典)} 个周期")
            
            return 结果字典
            
        except Exception as e:
            # 步骤5: 处理批量获取异常
            # 当批量获取过程中出现异常时，记录错误信息并返回空字典
            错误信息 = f"批量获取蜡烛数据失败: {e}"
            print(错误信息)
            logger.error(错误信息, exc_info=True)
            return {}
    
    def 测试连接(self) -> bool:
        """
        测试与OKX服务器的连接
        
        @return bool: 连接是否成功
        @exception Exception: 连接测试过程中可能发生的未知错误
        """
        try:
            # 步骤1: 记录开始测试
            # 记录开始测试连接的信息
            print("开始测试连接...")
            
            # 步骤2: 构建测试请求
            # 使用获取服务器时间接口测试连接
            # 这个接口不需要认证，适合用于连接测试
            请求路径 = "/api/v5/public/time"
            完整URL = f"{self.类_基础URL}{请求路径}"
            
            # 步骤3: 发送测试请求
            # 发送HTTP GET请求，设置5秒超时时间
            响应 = requests.get(完整URL, headers=self.类_请求头, timeout=5)
            
            # 步骤4: 检查响应结果
            # 检查HTTP状态码和API业务状态码
            # 如果都正确，则表示连接测试成功
            if 响应.status_code == 200:
                响应数据 = 响应.json()
                if 响应数据.get("code") == "0":
                    print("连接测试成功")
                    logger.info("连接测试成功")
                    return True
            
            # 步骤5: 记录测试失败
            # 如果测试失败，记录警告日志并返回False
            print("连接测试失败")
            logger.warning("连接测试失败")
            return False
            
        except Exception as e:
            # 步骤6: 处理异常
            # 当连接测试过程中出现异常时，记录错误信息并返回False
            错误信息 = f"连接测试失败: {e}"
            print(错误信息)
            logger.error(错误信息)
            return False
    
    def _解析蜡烛数据(self, 参_原始数据: List[str]) -> Dict[str, str]:
        """
        解析OKX API返回的原始蜡烛数据
        
        @param 参_原始数据 (List[str]): OKX API返回的原始数据列表
                [时间戳, 开盘价, 最高价, 最低价, 收盘价, 成交量, 成交额, 成交笔数]
            
        @return Dict[str, str]: 解析后的字典格式数据
        @exception ValueError: 当原始数据格式不正确时抛出
        @exception Exception: 解析过程中可能发生的未知错误
        """
        try:
            # 步骤1: 验证数据格式
            # 检查原始数据是否包含足够的字段
            # OKX API返回的蜡烛数据应该包含8个字段
            if len(参_原始数据) < 8:
                raise ValueError("原始数据格式不正确")
            
            # 步骤2: 构建解析结果
            # 将原始数据列表转换为字典格式
            # 使用中文键名，便于理解和维护
            return {
                "时间戳": 参_原始数据[0],
                "开盘价": 参_原始数据[1],
                "最高价": 参_原始数据[2],
                "最低价": 参_原始数据[3],
                "收盘价": 参_原始数据[4],
                "成交量": 参_原始数据[5],
                "成交额": 参_原始数据[6],
                "成交笔数": 参_原始数据[7]
            }
            
        except Exception as e:
            # 步骤3: 处理解析异常
            # 当解析过程中出现异常时，记录错误信息并重新抛出异常
            print(f"解析蜡烛数据失败: {e}")
            logger.error(f"解析蜡烛数据失败: {e}")
            raise
   
    def 转换交易对格式(self, 参_交易对: str, 参_交易所: str = "OKX") -> str:
        """
        将数据库中的交易对格式转换为交易所API所需的格式
        
        @param 参_交易对 (str): 数据库中的交易对格式，如 "BTC/USDT"
        @param 参_交易所 (str): 交易所名称，目前支持 "OKX" 和 "币安"
        @return str: 转换后的交易对格式
        @exception Exception: 转换过程中可能发生的未知错误
        """
        try:
            # 步骤1: 检查交易对格式
            if not 参_交易对 or '/' not in 参_交易对:
                logger.warning(f"交易对格式不正确: {参_交易对}")
                return 参_交易对  # 返回原始格式
            
            # 步骤2: 根据交易所进行格式转换
            if 参_交易所 == "OKX":
                # OKX使用 "-" 连接交易对
                return 参_交易对.replace('/', '-')
            elif 参_交易所 == "币安":
                # 币安不使用分隔符
                return 参_交易对.replace('/', '')
            else:
                # 不支持的交易所，返回原始格式
                logger.warning(f"不支持的交易所: {参_交易所}")
                return 参_交易对
                
        except Exception as e:
            # 步骤3: 处理异常
            logger.error(f"转换交易对格式失败: {e}")
            return 参_交易对  # 出错时返回原始格式


# 使用示例
if __name__ == "__main__":
    # 创建交易所实例
    交易所 = 类_OKX()
    
    # 测试交易对格式转换
    print("\n测试交易对格式转换:")
    原始交易对 = "BTC/USDT"
    OKX格式 = 交易所.转换交易对格式(原始交易对, "OKX")
    币安格式 = 交易所.转换交易对格式(原始交易对, "币安")
    print(f"原始交易对: {原始交易对}")
    print(f"OKX格式: {OKX格式}")
    print(f"币安格式: {币安格式}")
    
    # 测试连接
    if 交易所.测试连接():
        print("\n连接成功！")
        # 获取蜡烛数据
        蜡烛数据 = 交易所.获取蜡烛数据("BTC/USDT", "1m", 10)
        if 蜡烛数据:
            # 获取最新价格,用最新蜡烛的收盘价来确定最新价格
            最新价格 = 蜡烛数据[-1]['收盘价']
            print(f"BTC/USDT 最新价格: {最新价格}")
            print(f"获取到 {len(蜡烛数据)} 条蜡烛数据")
            for i, 数据 in enumerate(蜡烛数据[:3]):  # 只显示前3条
                print(f"第{i+1}条: 时间={数据['时间戳']}, 收盘价={数据['收盘价']}")
        
        # 测试批量获取蜡烛数据
        print("\n测试批量获取蜡烛数据:")
        周期列表 = ["1m", "5m", "15m", "1H"]
        批量蜡烛数据 = 交易所.批量获取蜡烛数据("BTC/USDT", 周期列表, 10)
        
        # 显示每个周期的数据数量
        for 周期, 数据列表 in 批量蜡烛数据.items():
            print(f"{周期} 周期: 获取到 {len(数据列表)} 条数据")
            if 数据列表:
                最新价格 = 数据列表[-1]['收盘价']
                print(f"{周期} 周期最新价格: {最新价格}")
    else:
        print("连接失败！")   