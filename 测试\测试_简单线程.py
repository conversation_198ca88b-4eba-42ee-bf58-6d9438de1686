# -*- coding: utf-8 -*-
"""
测试简单线程管理功能
"""

import time
from 模块类.功能类.类_线程管理 import 类_多线程管理器

def 测试基本功能():
    """
    测试基本的启动和停止功能
    """
    print("=== 开始测试简单线程管理器 ===")
    
    # 创建线程管理器
    管理器 = 类_多线程管理器()

    # 测试启动线程
    print("\n1. 测试启动线程...")
    成功1 = 管理器.启动线程("BOT001", "BTC-USDT")
    成功2 = 管理器.启动线程("BOT002", "ETH-USDT")
    
    print(f"启动结果: BOT001={成功1}, BOT002={成功2}")
    
    # 查看线程状态
    print("\n2. 查看线程状态...")
    状态字典 = 管理器.获取所有线程状态()
    print(f"所有线程状态: {状态字典}")
    
    # 让线程运行一段时间
    print("\n3. 让线程运行15秒...")
    time.sleep(15)
    
    # 停止一个线程
    print("\n4. 停止BOT001...")
    成功 = 管理器.停止线程("BOT001")
    print(f"停止结果: {成功}")
    
    # 再次查看状态
    print("\n5. 再次查看线程状态...")
    状态字典 = 管理器.获取所有线程状态()
    print(f"所有线程状态: {状态字典}")
    
    # 让剩余线程再运行一段时间
    print("\n6. 让剩余线程再运行10秒...")
    time.sleep(10)
    
    # 停止所有线程
    print("\n7. 停止所有线程...")
    管理器.停止所有线程()
    
    # 最终状态
    print("\n8. 最终状态...")
    状态字典 = 管理器.获取所有线程状态()
    print(f"所有线程状态: {状态字典}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    测试基本功能()
