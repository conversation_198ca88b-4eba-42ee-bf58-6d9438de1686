# -*- coding: utf-8 -*-
"""
状态栏管理类
负责管理主窗口状态栏的各种状态信息显示
"""

from PySide6.QtWidgets import QStatusBar, QLabel
from PySide6.QtCore import QTimer
import logging

# 获取当前模块的日志记录器
logger = logging.getLogger(__name__)

class 类_状态栏管理:
    """
    状态栏管理类
    负责管理状态栏中的系统提示信息显示
    """
    
    def __init__(self, 参_状态栏: QStatusBar):
        """
        初始化状态栏管理类
        
        参数:
            参_状态栏: 主窗口的状态栏对象
        """
        self.类_状态栏 = 参_状态栏
        
        # 创建系统提示标签
        self.类_系统提示标签 = QLabel("系统提示: 程序启动完成")
        self.类_状态栏.addWidget(self.类_系统提示标签, 1)  # 权重为1，占据大部分空间
        
        # 创建定时器用于清除临时消息
        self.类_清除定时器 = QTimer()
        self.类_清除定时器.timeout.connect(self.清除临时消息)
        
        logger.info("状态栏管理类初始化完成")
    
    def 显示系统提示(self, 参_消息: str, 参_超时毫秒: int = 5000):
        """
        显示系统提示消息（临时消息）
        
        参数:
            参_消息: 要显示的消息
            参_超时毫秒: 消息显示时间，默认5秒
        """
        self.类_系统提示标签.setText(f"系统提示: {参_消息}")
        
        # 停止之前的定时器
        self.类_清除定时器.stop()
        
        # 设置新的定时器
        if 参_超时毫秒 > 0:
            self.类_清除定时器.start(参_超时毫秒)
        
        logger.debug(f"状态栏显示消息: {参_消息}")
    
    def 清除临时消息(self):
        """
        清除临时消息，恢复默认状态
        """
        self.类_系统提示标签.setText("系统提示: 就绪")
        self.类_清除定时器.stop()
        logger.debug("状态栏消息已清除") 