---
alwaysApply: true
---

# Python项目开发规范

## 一、基本规范

1. **语言使用**：所有回复和代码使用中文，包括变量、函数、注释
2. **系统函数**：Python内置函数不要使用中文，以免造成程序出错
3. **称呼规范**：对我的称呼用"靓仔"
4. **开发者水平**：我是python的初学者,不要使用多态,继承,工厂模式等一些复杂的架构,不要使用链式调用等方法,要贴合python的初学者进行规划

## 二、项目结构规范（模块化设计）

### 2.1 简单模块化原则

1. **功能分离**：每个文件只做一件事
2. **简单明了**：代码容易理解和修改
3. **文件管理**：用文件夹来组织相关文件


## 三、命名规范

### 3.1 核心命名规则

| 变量类型     | 前缀  | 示例            | 说明                     |
|-------------|-------|----------------|--------------------------|
| **全局变量** | `全_` | `全_数据库连接` | 整个程序都能访问的变量    |
| **类的变量** | `类_` | `类_用户计数`   | 类中定义的变量           |
| **参数**     | `参_` | `参_用户名`     | 函数的输入参数           |
| **局部变量** | 无前缀 | `用户名`       | 函数内部使用的变量       |

### 3.2 文件和类命名

```python
# 文件命名
类_主窗口.py              # 类文件以"类_"开头
数据库管理.py             # 功能文件直接用功能名

# 类命名
class 类_主窗口:          # 类名以"类_"开头
class 类_数据库管理:      # 清晰表达类的功能
```

### 3.3 数据类型命名

```python
列表_用户数据 = []        # 列表类型
字典_配置信息 = {}        # 字典类型
集合_唯一值 = set()       # 集合类型
元组_坐标点 = ()          # 元组类型
```

## 四、代码组织规范

### 4.1 类内方法组织顺序

```python
class 类_示例:
    """类的说明"""
    
    # 1. 类变量
    类_版本 = "1.0"
    
    # 2. 初始化方法
    def __init__(self):
        pass
    
    # 3. 公共方法（按功能分组）
    def 获取数据(self):
        pass
    
    def 保存数据(self):
        pass
    
    # 4. 事件处理方法
    def 槽_按钮点击(self):
        pass
    
    # 5. 私有方法
    def _内部处理(self):
        pass
    
    # 6. 静态方法和类方法
    @staticmethod
    def 工具方法():
        pass
```

## 五、错误处理规范

```python
def 安全执行方法(self, 参_操作参数):
    """带异常处理的方法示例"""
    try:
        # 主要业务逻辑
        结果 = self.执行业务逻辑(参_操作参数)
        logging.info(f"[{self.__class__.__name__}] 业务逻辑执行成功")
        return 结果
        
    except ValueError as e:
        logging.warning(f"[{self.__class__.__name__}] 参数错误: {e}")
        return None
        
    except Exception as e:
        logging.error(f"[{self.__class__.__name__}] 执行过程中发生未知错误", exc_info=True)
        return None
```

## 六、模块间通信规范

```python
from PySide6.QtCore import Signal, QObject

class 类_事件管理(QObject):
    """事件管理类，负责模块间通信"""
    
    信号_数据更新 = Signal(str, dict)
    信号_状态变化 = Signal(str)
    
    def 发送数据更新信号(self, 参_数据类型, 参_数据):
        """发送数据更新信号"""
        self.信号_数据更新.emit(参_数据类型, 参_数据)
```

## 七、函数备注规范


### 7.1 备注编写要点
1. **使用装饰器**：统一使用 `@param`、`@return`、`@exception` 装饰器
2. **类型标注**：参数和返回值都要标注类型
3. **信息完整**：必须包含参数说明、返回值说明、异常说明
4. **格式统一**：保持一致的缩进和格式
5. **中文优先**：所有说明文字使用中文

### 7.2 函数内部步骤备注规范

```python
def 处理用户数据(self, 参_用户列表: list) -> dict:
    """
    处理用户数据列表，统计各种信息
    
    @param 参_用户列表 (list): 用户数据列表
    @return dict: 统计结果
    @exception ValueError: 当用户列表为空时抛出
    """
    # 步骤1: 输入验证
    # 检查用户列表是否为空，确保有数据可以处理
    # 如果列表为空，抛出异常并停止执行
    if not 参_用户列表:
        raise ValueError("用户列表不能为空")
    
    # 步骤2: 初始化统计变量
    # 创建字典来存储各种统计数据
    # 这些变量将在后续循环中累加数据
    统计结果 = {
        "总用户数": 0,
        "活跃用户数": 0,
        "用户年龄总和": 0
    }
    
    # 步骤3: 遍历用户数据
    # 逐个处理每个用户的数据，进行统计计算
    # 这个循环会遍历列表中的每个用户对象
    for 用户 in 参_用户列表:
        # 增加总用户数计数
        # 每处理一个用户，总用户数加1
        统计结果["总用户数"] += 1
        
        # 检查用户是否活跃
        # 如果用户状态为"活跃"，则活跃用户数加1
        if 用户.get("状态") == "活跃":
            统计结果["活跃用户数"] += 1
        
        # 累加用户年龄
        # 将当前用户的年龄加到总和中，用于后续计算平均年龄
        统计结果["用户年龄总和"] += 用户.get("年龄", 0)
    
    # 步骤4: 计算平均值
    # 根据总用户数和年龄总和计算平均年龄
    # 避免除零错误，如果总用户数为0则平均年龄为0
    if 统计结果["总用户数"] > 0:
        统计结果["平均年龄"] = 统计结果["用户年龄总和"] / 统计结果["总用户数"]
    else:
        统计结果["平均年龄"] = 0
    
    # 步骤5: 返回统计结果
    # 将完整的统计结果返回给调用者
    # 包含总用户数、活跃用户数、平均年龄等信息
    return 统计结果
```

### 7.3 步骤备注编写规则
1. **步骤编号**：使用 `# 步骤1:`、`# 步骤2:` 等格式
2. **步骤描述**：每个步骤详细描述要做什么
3. **详细说明**：在步骤编号后添加具体说明，解释这一步具体在做什么
4. **关键逻辑**：在复杂逻辑前添加说明注释
5. **错误处理**：在可能出错的地方添加说明
6. **返回值**：在返回前说明返回的内容

```
