# -*- coding: utf-8 -*-
"""
策略编辑窗口类
用于编辑现有策略
"""

import sys
import os
import logging
from PySide6.QtWidgets import QDialog, QMessageBox
from 窗口UI布局.UI文件.Strategy_Edit_ui import Ui_StrategyEditWindow
import json

# 获取当前模块的日志记录器
logger = logging.getLogger(__name__)

class 类_策略编辑窗口(QDialog, Ui_StrategyEditWindow):
    """
    策略编辑窗口类
    用于编辑现有策略
    """
    
    def __init__(self, 参_机器人编号, 参_父窗口=None, 参_策略参数服务=None):
        """
        初始化策略编辑窗口
        
        参数:
            参_机器人编号: 要编辑的机器人编号
            参_父窗口: 父窗口对象
            参_策略参数服务: 策略参数服务（可选）
        """
        super().__init__(参_父窗口)
        
        # 保存机器人编号和父窗口引用
        self.类_机器人编号 = 参_机器人编号
        self.类_父窗口 = 参_父窗口
        
        # 初始化变量
        self.类_机器人数据 = {}
        self.类_策略参数 = {}
        self.类_模块名 = "策略编辑窗口"
        
        # 初始化策略参数服务
        self.初始化策略参数服务(参_策略参数服务)
        
        # 设置UI界面
        self.setupUi(self)
        
        # 设置窗口标题
        self.setWindowTitle(f"编辑策略 - {self.类_机器人编号}")
        
        # 设置按钮文本为中文
        self.buttonBox.button(self.buttonBox.StandardButton.Save).setText("保存")
        self.buttonBox.button(self.buttonBox.StandardButton.Cancel).setText("取消")
        
        # 加载机器人数据
        self.加载机器人数据()
        
        # 连接信号槽
        self.连接信号槽()
        
        logger.info(f"策略编辑窗口初始化完成，机器人编号: {self.类_机器人编号}")
        print(f"策略编辑窗口初始化完成，机器人编号: {self.类_机器人编号}")
    
    def 初始化策略参数服务(self, 参_策略参数服务=None):
        """
        初始化策略参数服务
        
        参数:
            参_策略参数服务: 外部注入的策略参数服务
        """
        if 参_策略参数服务:
            self.类_策略参数服务 = 参_策略参数服务
            logger.info("使用注入的策略参数服务")
            print("使用注入的策略参数服务")
        else:
            # 创建默认的策略参数服务
            try:
                # 添加项目根目录到sys.path
                当前目录 = os.path.dirname(os.path.abspath(__file__))
                项目根目录 = os.path.dirname(os.path.dirname(当前目录))
                if 项目根目录 not in sys.path:
                    sys.path.insert(0, 项目根目录)
                
                # 使用类_AI策略参数代替类_策略参数服务
                from 模块类.功能类.类_AI策略参数 import 类_AI策略参数
                self.类_策略参数服务 = 类_AI策略参数()
                logger.info("创建默认策略参数服务成功")
                print("创建默认策略参数服务")
            except ImportError as e:
                logger.error(f"无法导入策略参数服务: {e}", exc_info=True)
                print(f"无法导入策略参数服务: {e}")
                self.类_策略参数服务 = None
    
    def 连接信号槽(self):
        """
        连接信号槽
        """
        # 连接确定和取消按钮
        self.buttonBox.accepted.connect(self.槽_确定)
        self.buttonBox.rejected.connect(self.reject)
        
        # 连接单选按钮的信号槽
        self.radioButton_edit_parameters.toggled.connect(self.槽_编辑模式切换)
        self.radioButton_change_strategy.toggled.connect(self.槽_编辑模式切换)
        
        # 连接AI策略类型切换的信号槽
        self.radioButton_change_aggressive.toggled.connect(self.槽_AI策略类型切换)
        self.radioButton_change_defensive.toggled.connect(self.槽_AI策略类型切换)
        self.radioButton_change_universal.toggled.connect(self.槽_AI策略类型切换)
    
    def 加载机器人数据(self):
        """
        从数据库加载机器人数据
        """
        try:
            # 获取父窗口的数据库管理对象
            if not self.类_父窗口 or not hasattr(self.类_父窗口, '类_数据库管理'):
                logger.error("无法获取数据库管理对象")
                raise ValueError("无法获取数据库管理对象")
            
            数据库管理 = self.类_父窗口.类_数据库管理
            
            # 获取机器人数据
            if hasattr(数据库管理, '类_机器人数据访问'):
                # 使用重构后的机器人数据访问
                机器人数据 = 数据库管理.类_机器人数据访问.获取机器人(self.类_机器人编号)
            else:
                # 兼容旧版数据库管理类
                机器人数据 = 数据库管理.获取机器人(self.类_机器人编号)
            
            if not 机器人数据:
                logger.error(f"未找到机器人数据: {self.类_机器人编号}")
                raise ValueError(f"未找到机器人数据: {self.类_机器人编号}")
            
            # 保存机器人数据
            self.类_机器人数据 = 机器人数据
            
            # 设置交易对
            self.label_trading_pair_value.setText(机器人数据.get("交易对", ""))
            
            # 获取策略配置类型
            策略配置 = 机器人数据.get("策略配置", "自定义")
            
            # 解析策略参数
            try:
                # 尝试获取策略参数，优先顺序：策略参数dict > 策略参数JSON > 子策略
                策略参数 = None
                
                # 1. 优先尝试从策略参数字段获取(dict格式)
                if "策略参数" in 机器人数据 and isinstance(机器人数据["策略参数"], dict):
                    策略参数 = 机器人数据["策略参数"]
                # 2. 尝试从策略参数JSON字段获取
                elif "策略参数JSON" in 机器人数据 and 机器人数据["策略参数JSON"]:
                    策略参数 = json.loads(机器人数据["策略参数JSON"])
                # 3. 尝试从子策略字段获取
                elif "子策略" in 机器人数据 and isinstance(机器人数据["子策略"], str):
                    try:
                        策略参数 = json.loads(机器人数据["子策略"])
                    except:
                        # 如果子策略不是JSON字符串，则可能是普通字符串
                        策略参数 = {"子策略": 机器人数据["子策略"]}
                
                # 如果仍未获取到策略参数，使用空字典
                if 策略参数 is None:
                    策略参数 = {}
                
                # 根据策略配置类型设置参数
                if 策略配置 == "AI策略":
                    self.设置AI策略参数(策略参数)
                    logger.info("设置AI策略参数成功")
                    print("设置AI策略参数")
                elif 策略配置 == "自定义":
                    self.设置自定义策略参数(策略参数)
                    logger.info("设置自定义策略参数成功")
                    print("设置自定义策略参数")
                else:
                    logger.warning(f"未知的策略配置类型: {策略配置}")
                    print(f"未知的策略配置类型: {策略配置}")
                    
            except json.JSONDecodeError as e:
                logger.error(f"策略参数解析失败: {e}", exc_info=True)
                print(f"策略参数解析失败: {e}")
                # 如果解析失败，根据策略配置类型设置默认参数
                if 策略配置 == "AI策略":
                    self.设置AI策略参数({})
                elif 策略配置 == "自定义":
                    self.设置自定义策略参数({})
            except Exception as e:
                logger.error(f"处理策略参数时出错: {e}", exc_info=True)
                print(f"处理策略参数时出错: {e}")
                # 设置默认参数
                if 策略配置 == "AI策略":
                    self.设置AI策略参数({})
                elif 策略配置 == "自定义":
                    self.设置自定义策略参数({})
            
            # 显示对应的编辑界面
            self.显示策略编辑界面(策略配置)
            
            logger.info(f"机器人数据加载完成: {self.类_机器人编号}")
            print(f"机器人数据加载完成: {self.类_机器人编号}")
            
        except Exception as e:
            logger.error(f"加载机器人数据时发生错误: {e}", exc_info=True)
            print(f"加载机器人数据时发生错误: {e}")
            QMessageBox.critical(self, "错误", f"加载机器人数据失败: {e}")
            self.reject()  # 关闭窗口
    
    def 槽_编辑模式切换(self):
        """
        编辑模式切换槽函数
        """
        # 获取当前策略类型
        策略配置 = self.类_机器人数据.get("策略配置", "")
        
        # 根据策略类型设置单选按钮状态和页面显示
        if 策略配置 == "AI策略":
            # AI策略：只能修改AI策略参数
            self.radioButton_change_strategy.setChecked(True)
            self.radioButton_edit_parameters.setEnabled(False)
            self.stackedWidget_edit_content.setCurrentIndex(1)  # 显示AI策略编辑页面
        elif 策略配置 == "自定义":
            # 自定义策略：只能修改自定义参数
            self.radioButton_edit_parameters.setChecked(True)
            self.radioButton_change_strategy.setEnabled(False)
            self.stackedWidget_edit_content.setCurrentIndex(0)  # 显示自定义策略编辑页面
        
        print(f"编辑模式切换: {策略配置}")
    
    def 槽_AI策略类型切换(self):
        """
        AI策略类型切换槽函数
        控制子策略选项的显示和隐藏
        """
        # 根据选择的策略类型显示对应的子策略选项
        if self.radioButton_change_universal.isChecked():
            # 通用型策略：显示通用型子策略，隐藏普通子策略
            self.radioButton_change_universal_a.setVisible(True)
            self.radioButton_change_crazy.setVisible(False)
            self.radioButton_change_aggressive_sub.setVisible(False)
            self.radioButton_change_balanced.setVisible(False)
            self.radioButton_change_stable.setVisible(False)
            self.radioButton_change_conservative.setVisible(False)
            # 默认选择通用型子策略
            self.radioButton_change_universal_a.setChecked(True)
        else:
            # 进攻型或防守型策略：显示普通子策略，隐藏通用型子策略
            self.radioButton_change_universal_a.setVisible(False)
            self.radioButton_change_crazy.setVisible(True)
            self.radioButton_change_aggressive_sub.setVisible(True)
            self.radioButton_change_balanced.setVisible(True)
            self.radioButton_change_stable.setVisible(True)
            self.radioButton_change_conservative.setVisible(True)
            # 默认选择疯狂型子策略
            self.radioButton_change_crazy.setChecked(True)
        
        print("AI策略类型切换完成")
    
    def 显示策略编辑界面(self, 参_策略类型: str):
        """
        根据策略类型显示对应的编辑界面
        
        参数:
            参_策略类型: 策略类型（"AI策略" 或 "自定义"）
        """
        print(f"显示策略编辑界面: {参_策略类型}")
        
        # 根据策略类型显示不同的编辑界面
        if 参_策略类型 == "AI策略":
            # 显示AI策略编辑界面
            self.显示AI策略编辑界面()
        elif 参_策略类型 == "自定义":
            # 显示自定义策略编辑界面
            self.显示自定义策略编辑界面()
        else:
            print(f"未知的策略类型: {参_策略类型}")
    
    def 显示AI策略编辑界面(self):
        """
        显示AI策略编辑界面
        """
        print("显示AI策略编辑界面")
        
        # 设置单选按钮状态
        self.radioButton_change_strategy.setChecked(True)
        self.radioButton_edit_parameters.setEnabled(False)
        
        # 切换到AI策略编辑页面
        self.stackedWidget_edit_content.setCurrentIndex(1)
        
        print("AI策略编辑界面已显示")
    
    def 显示自定义策略编辑界面(self):
        """
        显示自定义策略编辑界面
        """
        print("显示自定义策略编辑界面")
        
        # 设置单选按钮状态
        self.radioButton_edit_parameters.setChecked(True)
        self.radioButton_change_strategy.setEnabled(False)
        
        # 切换到自定义策略编辑页面
        self.stackedWidget_edit_content.setCurrentIndex(0)
        
        print("自定义策略编辑界面已显示")
    
    def 设置自定义策略参数(self, 参_策略参数: dict):
        """
        设置自定义策略参数到界面上
        
        参数:
            参_策略参数: 策略参数字典
        """
        try:
            # 设置自定义策略参数
            self.doubleSpinBox_edit_first_amount.setValue(float(参_策略参数.get("首单金额", 0)))
            self.doubleSpinBox_edit_drop_percent.setValue(float(参_策略参数.get("跌幅加仓", 0)))
            self.doubleSpinBox_edit_position_multiplier.setValue(float(参_策略参数.get("加仓倍数", 0)))
            self.doubleSpinBox_edit_profit_percent.setValue(float(参_策略参数.get("止盈比例", 0)))
            self.spinBox_edit_max_orders.setValue(int(参_策略参数.get("最大订单", 0)))
            self.doubleSpinBox_edit_profit_stop.setValue(float(参_策略参数.get("止盈金额", 0)))
            self.checkBox_edit_dynamic_position.setChecked(bool(参_策略参数.get("动态持仓", False)))
            self.doubleSpinBox_edit_rebound_percent.setValue(float(参_策略参数.get("反弹补仓", 0)))
            
            # 保存策略参数
            self.类_策略参数 = 参_策略参数
            
            logger.info("自定义策略参数设置完成")
            print("自定义策略参数设置完成")
            
        except Exception as e:
            logger.error(f"策略参数数据错误: {e}", exc_info=True)
            print(f"策略参数数据错误: {e}")
    
    def 设置AI策略参数(self, 参_策略参数: dict):
        """
        设置AI策略参数
        
        参数:
            参_策略参数: 策略参数字典
        """
        print("设置AI策略参数")
        print(f"AI策略参数: {参_策略参数}")
        
        # 保存策略参数到类变量
        self.类_策略参数 = 参_策略参数
        
        # 设置AI策略的参数到对应控件
        try:
            # 策略类型选择
            策略类型 = 参_策略参数.get("策略类型", "进攻型策略")
            if 策略类型 == "进攻型策略":
                self.radioButton_change_aggressive.setChecked(True)
            elif 策略类型 == "防守型策略":
                self.radioButton_change_defensive.setChecked(True)
            elif 策略类型 == "通用型策略":
                self.radioButton_change_universal.setChecked(True)
            
            # 子策略选择
            子策略 = 参_策略参数.get("子策略", "疯狂型")
            if 子策略 == "疯狂型":
                self.radioButton_change_crazy.setChecked(True)
            elif 子策略 == "激进型":
                self.radioButton_change_aggressive_sub.setChecked(True)
            elif 子策略 == "平衡型":
                self.radioButton_change_balanced.setChecked(True)
            elif 子策略 == "稳健型":
                self.radioButton_change_stable.setChecked(True)
            elif 子策略 == "保守型":
                self.radioButton_change_conservative.setChecked(True)
            elif 子策略 == "A" or 子策略 == "策略A":  # 兼容两种命名
                self.radioButton_change_universal_a.setChecked(True)
            
            # 投入本金
            投入本金 = 参_策略参数.get("投入本金", 100.0)
            self.doubleSpinBox_change_capital.setValue(float(投入本金))
            
            print("AI策略参数设置完成")
            
        except (KeyError, ValueError, TypeError) as e:
            print(f"AI策略参数数据错误: {e}")
            # 如果参数设置失败，使用默认值
            self.radioButton_change_aggressive.setChecked(True)
            self.radioButton_change_crazy.setChecked(True)
            self.doubleSpinBox_change_capital.setValue(100.0)
    
    def 槽_确定(self):
        """
        确定按钮点击事件
        保存修改后的策略参数
        """
        print(f"保存策略修改: {self.类_机器人编号}")
        
        # 获取修改后的数据
        修改数据 = self.收集修改数据()
        
        if not 修改数据:
            return
        
        # 保存修改到数据库
        if self.保存修改数据(修改数据):
            # 使用父窗口的状态栏显示成功消息
            if self.类_父窗口 and hasattr(self.类_父窗口, '类_状态栏管理'):
                self.类_父窗口.类_状态栏管理.显示系统提示(f"策略 {self.类_机器人编号} 修改已保存")
            self.accept()
        else:
            QMessageBox.critical(self, "保存失败", "策略修改保存失败，请检查数据！")
    
    def 收集修改数据(self) -> dict | None:
        """
        收集修改后的数据
        现在返回完整的机器人数据，相当于重新配置策略
        
        返回:
            修改后的数据字典，如果验证失败返回None
        """
        try:
            # 获取策略配置类型
            策略配置 = self.类_机器人数据.get("策略配置", "")
            
            # 根据策略类型收集不同的数据
            if 策略配置 == "AI策略":
                新机器人数据 = self.收集AI策略数据()
            elif 策略配置 == "自定义":
                新机器人数据 = self.收集自定义策略数据()
            else:
                print(f"未知的策略配置类型: {策略配置}")
                return None
            
            # 如果收集数据失败，返回None
            if not 新机器人数据:
                return None
            
            # 保留原机器人的编号和一些状态信息
            新机器人数据["机器人编号"] = self.类_机器人编号
            
            # 保留当前的运行状态和收益数据（如果有的话）
            原数据 = self.类_机器人数据
            if 原数据:
                # 保留运行时的重要数据
                新机器人数据["累计下单"] = 原数据.get("累计下单", 0)
                新机器人数据["订单数量"] = 原数据.get("订单数量", 0)
                新机器人数据["浮动收益"] = 原数据.get("浮动收益", 0)
                新机器人数据["浮动收益率"] = 原数据.get("浮动收益率", 0)
                新机器人数据["结算收益"] = 原数据.get("结算收益", 0)
                新机器人数据["持仓均价"] = 原数据.get("持仓均价", 0)
                新机器人数据["持仓数量"] = 原数据.get("持仓数量", 0)
                新机器人数据["预估平仓价格"] = 原数据.get("预估平仓价格", 0)
                新机器人数据["预估月化率"] = 原数据.get("预估月化率", 0)
                新机器人数据["预估年化率"] = 原数据.get("预估年化率", 0)
                新机器人数据["策略状态"] = 原数据.get("策略状态", "停止")
                新机器人数据["当前状态"] = 原数据.get("当前状态", "待机")
                新机器人数据["创建时间"] = 原数据.get("创建时间", "")
            
            return 新机器人数据
                
        except Exception as e:
            print(f"收集修改数据时发生错误: {e}")
            return None
    
    def 收集AI策略数据(self) -> dict:
        """
        收集AI策略参数
        
        返回:
            AI策略参数字典
        """
        # 获取当前选择的策略类型
        策略类型 = "通用型"  # 默认
        if self.radioButton_change_aggressive.isChecked():
            策略类型 = "进攻型"
        elif self.radioButton_change_defensive.isChecked():
            策略类型 = "防守型"
        elif self.radioButton_change_universal.isChecked():
            策略类型 = "通用型"
        
        # 获取当前选择的子策略
        子策略 = "平衡型"  # 默认
        if self.radioButton_change_crazy.isChecked():
            子策略 = "疯狂型"
        elif self.radioButton_change_aggressive_sub.isChecked():
            子策略 = "激进型"
        elif self.radioButton_change_balanced.isChecked():
            子策略 = "平衡型"
        elif self.radioButton_change_stable.isChecked():
            子策略 = "稳健型"
        elif self.radioButton_change_conservative.isChecked():
            子策略 = "保守型"
        elif self.radioButton_change_universal_a.isChecked():
            子策略 = "A"
        
        # 获取投入本金
        投入本金 = self.doubleSpinBox_change_capital.value()
        
        # 获取AI策略参数
        AI策略参数 = None
        try:
            if hasattr(self, '类_策略参数服务') and self.类_策略参数服务:
                AI策略参数 = self.类_策略参数服务.获取策略参数(策略类型, 子策略)
        except Exception as e:
            logger.error(f"无法获取AI策略参数: {e}", exc_info=True)
            AI策略参数 = None
        
        if not AI策略参数:
            logger.warning(f"未找到策略类型 {策略类型} 下的子策略 {子策略} 参数配置")
            # 使用默认参数
            AI策略参数 = {}
        
        # 确保AI策略参数中包含投入本金
        AI策略参数["投入本金"] = 投入本金
        
        # 计算预配首单,保留4位小数
        预配首单 = round(投入本金 / AI策略参数.get("复利分母", 1), 4)
        
        # 构建完整的机器人数据（类似策略配置窗口的逻辑）
        机器人数据 = {
            "交易对": self.类_机器人数据.get("交易对", ""),
            "预配本金": 投入本金,
            "预配首单": 预配首单,
            "策略配置": "AI策略",
            "策略类型": 策略类型,
            "子策略": 子策略,
            "最大订单": AI策略参数.get("最大持仓单数", 0),
            # 保存完整的AI策略参数配置到策略参数JSON字段
            "策略参数JSON": json.dumps({
                "投入本金": 投入本金,
                "策略类型": 策略类型,
                "子策略": 子策略,
                **AI策略参数
            }, ensure_ascii=False)
        }
        
        return 机器人数据
    
    def 收集自定义策略数据(self) -> dict:
        """
        收集自定义策略的修改数据
        复用策略配置窗口的逻辑
        
        返回:
            自定义策略的修改数据字典
        """
        print("收集自定义策略数据")
        
        try:
            # 获取自定义策略参数
            首单金额 = self.doubleSpinBox_edit_first_amount.value()
            跌幅加仓 = self.doubleSpinBox_edit_drop_percent.value()
            加仓倍数 = self.doubleSpinBox_edit_position_multiplier.value()
            止盈比例 = self.doubleSpinBox_edit_profit_percent.value()
            最大订单 = self.spinBox_edit_max_orders.value()
            止盈金额 = self.doubleSpinBox_edit_profit_stop.value()
            动态持仓 = self.checkBox_edit_dynamic_position.isChecked()
            反弹补仓 = self.doubleSpinBox_edit_rebound_percent.value()
            
            # 验证数据
            if 首单金额 <= 0:
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.warning(self, "输入错误", "首单金额必须大于0！")
                return {}
            
            # 构建完整的机器人数据（类似策略配置窗口的逻辑）
            机器人数据 = {
                "交易对": self.类_机器人数据.get("交易对", ""),
                "预配本金": 首单金额,  # 使用首单金额作为预配本金
                "预配首单": 首单金额,
                "本次下单": 0,
                "复利金额": 0,
                "累计下单": 0,
                "订单数量": 0,
                "最大订单": 最大订单,
                "浮动收益": 0,
                "浮动收益率": 0,
                "结算收益": 0,
                "持仓均价": 0,
                "持仓数量": 0,
                "预估平仓价格": 0,
                "预估月化率": 0,
                "预估年化率": 0,
                "策略状态": "停止",
                "当前状态": "待机",
                "策略配置": "自定义",
                "策略类型": "自定义",
                "子策略": "自定义",  # 自定义策略的子策略就是"自定义"
                # 保存策略参数到策略参数JSON字段
                "策略参数JSON": json.dumps({
                    "首单金额": 首单金额,
                    "跌幅加仓": 跌幅加仓,
                    "加仓倍数": 加仓倍数,
                    "止盈比例": 止盈比例,
                    "最大订单": 最大订单,
                    "止盈金额": 止盈金额,
                    "动态持仓": 动态持仓,
                    "反弹补仓": 反弹补仓
                }, ensure_ascii=False)
            }
            
            print(f"收集到的自定义策略数据: {机器人数据}")
            return 机器人数据
            
        except Exception as e:
            print(f"收集自定义策略数据时发生错误: {e}")
            return {}
    
    def 保存修改数据(self, 参_修改数据: dict) -> bool:
        """
        保存修改后的数据到数据库
        
        参数:
            参_修改数据: 修改后的数据字典
            
        返回:
            保存是否成功
        """
        if not self.类_父窗口 or not hasattr(self.类_父窗口, '类_数据库管理') or not self.类_父窗口.类_数据库管理:
            print("无法获取数据库管理对象")
            return False
        
        try:
            # 更新数据库中的机器人数据
            成功 = self.类_父窗口.类_数据库管理.更新机器人(self.类_机器人编号, 参_修改数据)
            
            if 成功:
                print(f"机器人数据更新成功: {self.类_机器人编号}")
            else:
                print(f"机器人数据更新失败: {self.类_机器人编号}")
            
            return 成功
            
        except Exception as e:
            print(f"保存修改数据时发生错误: {e}")
            return False 