# -*- coding: utf-8 -*-
"""
策略配置窗口类
用于新建策略
"""

import sys
import os
import logging
from PySide6.QtWidgets import QDialog, QMessageBox
from 窗口UI布局.UI文件.Strategy_Config_ui import Ui_StrategyConfigWindow
import json

# 获取当前模块的日志记录器
logger = logging.getLogger(__name__)

class 类_策略配置窗口(QDialog, Ui_StrategyConfigWindow):
    """
    策略配置窗口类
    用于新建策略
    """
    
    def __init__(self, 参_父窗口=None, 参_数据库管理器=None):
        """
        初始化策略配置窗口
        
        参数:
            参_父窗口: 父窗口对象
            参_数据库管理器: 注入的数据库管理器（可选）
        """
        super().__init__(参_父窗口)
        
        # 保存父窗口引用
        self.类_父窗口 = 参_父窗口
        
        # 注入的数据库管理器
        self.类_数据库管理器 = 参_数据库管理器
        
        # 设置模块名称
        self.类_模块名 = "策略配置窗口"
        
        # 初始化策略参数服务
        self.初始化策略参数服务()
        
        # 设置UI界面
        self.setupUi(self)
        
        # 设置窗口标题
        self.setWindowTitle("新建策略配置")
        
        # 确保子策略选择框默认选中平衡型
        self.radioButton_balanced.setChecked(True)
        
        # 连接信号槽
        self.连接信号槽()
        
        # 加载交易对列表
        self.加载交易对列表()
        
        # 设置按钮文本为中文
        self.pushButton_add_strategy.setText("添加策略")
        self.pushButton_add_all.setText("添加全部")
        
        logger.info("策略配置窗口初始化完成")
        print("策略配置窗口初始化完成")
    
    def 获取数据库管理器(self):
        """
        获取数据库管理器
        优先使用注入的数据库管理器，其次使用父窗口的数据库管理器
        
        返回:
            数据库管理器对象或None
        """
        # 优先使用注入的数据库管理器
        if self.类_数据库管理器:
            return self.类_数据库管理器
        
        # 其次使用父窗口的数据库管理器
        if (self.类_父窗口 and 
            hasattr(self.类_父窗口, '类_数据库管理') and 
            self.类_父窗口.类_数据库管理):
            return self.类_父窗口.类_数据库管理
        
        return None
    
    def 初始化策略参数服务(self):
        """
        初始化策略参数服务
        """
        try:
            # 添加项目根目录到sys.path
            当前目录 = os.path.dirname(os.path.abspath(__file__))
            项目根目录 = os.path.dirname(os.path.dirname(os.path.dirname(当前目录)))
            if 项目根目录 not in sys.path:
                sys.path.insert(0, 项目根目录)
            
            # 使用类_AI策略参数代替类_策略参数服务
            from 模块类.功能类.类_AI策略参数 import 类_AI策略参数
            self.类_策略参数服务 = 类_AI策略参数()
            logger.info("创建默认策略参数服务成功")
            print("策略配置窗口：创建默认策略参数服务")
        except ImportError as e:
            logger.error(f"无法导入策略参数服务: {e}", exc_info=True)
            print(f"无法导入策略参数服务: {e}")
            self.类_策略参数服务 = None
    
    def 连接信号槽(self):
        """
        连接信号槽
        """
        # 连接确定和取消按钮
        self.pushButton_add_strategy.clicked.connect(self.槽_确定)
        # 注意：取消按钮已在UI文件中连接到reject
        
        # 连接策略类型切换的信号槽
        self.radioButton_aggressive.toggled.connect(self.槽_策略类型切换)
        self.radioButton_defensive.toggled.connect(self.槽_策略类型切换)
        self.radioButton_universal.toggled.connect(self.槽_策略类型切换)
        
        # 连接添加全部按钮的信号槽
        self.pushButton_add_all.clicked.connect(self.槽_添加全部)
        
        # 初始化策略类型显示
        self.槽_策略类型切换()
    
    def 槽_策略类型切换(self):
        """
        策略类型切换槽函数
        控制子策略选项的显示和隐藏
        """
        # 根据选择的策略类型显示对应的子策略选项
        if self.radioButton_universal.isChecked():
            # 通用型策略：显示通用型子策略，隐藏普通子策略
            self.radioButton_universal_a.setVisible(True)
            self.radioButton_crazy.setVisible(False)
            self.radioButton_aggressive_sub.setVisible(False)
            self.radioButton_balanced.setVisible(False)
            self.radioButton_stable.setVisible(False)
            self.radioButton_conservative.setVisible(False)
            # 默认选择通用型子策略
            self.radioButton_universal_a.setChecked(True)
        else:
            # 进攻型或防守型策略：显示普通子策略，隐藏通用型子策略
            self.radioButton_universal_a.setVisible(False)
            self.radioButton_crazy.setVisible(True)
            self.radioButton_aggressive_sub.setVisible(True)
            self.radioButton_balanced.setVisible(True)
            self.radioButton_stable.setVisible(True)
            self.radioButton_conservative.setVisible(True)
            # 默认选择平衡型子策略
            self.radioButton_balanced.setChecked(True)
        
        print("策略类型切换完成")
    
    def 加载交易对列表(self):
        """
        加载交易对列表到comboBox中
        根据当前选择的交易所加载对应的交易对
        """
        # 获取当前选择的交易所
        当前交易所 = self.获取当前交易所()
        
        # 清空现有选项
        self.comboBox_trading_pair.clear()
        
        # 获取数据库管理器
        数据库管理器 = self.获取数据库管理器()
        
        if 数据库管理器:
            try:
                # 检查是否是重构后的数据库管理类
                if hasattr(数据库管理器, '类_交易对数据访问'):
                    # 使用重构后的交易对数据访问
                    交易对列表 = 数据库管理器.类_交易对数据访问.获取交易对列表(当前交易所)
                else:
                    # 兼容旧版数据库管理类
                    交易对列表 = 数据库管理器.获取交易对列表(当前交易所)
                
                # 添加交易对到comboBox
                for 交易对数据 in 交易对列表:
                    self.comboBox_trading_pair.addItem(交易对数据['交易对'])
                
                logger.info(f"已加载 {len(交易对列表)} 个 {当前交易所} 交易对")
                print(f"已加载 {len(交易对列表)} 个 {当前交易所} 交易对")
            except Exception as e:
                logger.error(f"加载交易对列表失败: {e}", exc_info=True)
                print(f"加载交易对列表失败: {e}")
                # 使用默认交易对
                默认交易对 = ['BTC/USDT', 'ETH/USDT', 'SOL/USDT']
                for 交易对 in 默认交易对:
                    self.comboBox_trading_pair.addItem(交易对)
        else:
            logger.warning("无法获取数据库管理对象，使用默认交易对")
            print("无法获取数据库管理对象，使用默认交易对")
            # 添加默认交易对
            默认交易对 = ['BTC/USDT', 'ETH/USDT', 'SOL/USDT']
            for 交易对 in 默认交易对:
                self.comboBox_trading_pair.addItem(交易对)
    
    def 获取当前交易所(self) -> str:
        """
        获取当前选择的交易所
        
        返回:
            当前交易所名称
        """
        if self.类_父窗口 and hasattr(self.类_父窗口, 'radioButton_Binance') and hasattr(self.类_父窗口, 'radioButton_OKX'):
            if self.类_父窗口.radioButton_Binance.isChecked():
                return "币安"
            elif self.类_父窗口.radioButton_OKX.isChecked():
                return "OKX"
        
        # 默认返回币安
        return "币安"
    
    def 槽_确定(self):
        """
        确定按钮点击事件
        """
        logger.info("新建策略确定")
        print("新建策略确定")
        
        # 获取表单数据
        机器人数据 = self.收集表单数据()
        
        # 验证数据
        if not 机器人数据:
            return
        
        # 保存策略数据到数据库
        if self.保存机器人数据(机器人数据):
            # 使用父窗口的状态栏显示成功消息
            if self.类_父窗口 and hasattr(self.类_父窗口, '类_状态栏管理'):
                self.类_父窗口.类_状态栏管理.显示系统提示(f"策略配置已保存: {机器人数据.get('交易对', '')}")
            # 接受对话框
            self.accept()
        else:
            QMessageBox.critical(self, "保存失败", "策略配置保存失败，请检查数据！")
    
    def 槽_添加全部(self):
        """
        添加全部按钮点击事件
        批量添加所有交易对的策略
        """
        logger.info("开始批量添加所有交易对的策略")
        print("开始批量添加所有交易对的策略")
        
        # 获取所有交易对
        交易对数量 = self.comboBox_trading_pair.count()
        if 交易对数量 == 0:
            logger.warning("没有可用的交易对")
            QMessageBox.warning(self, "错误", "没有可用的交易对！")
            return
        
        # 确认是否批量添加
        回复 = QMessageBox.question(self, "确认", f"确定要为所有{交易对数量}个交易对添加策略吗？",
                                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        if 回复 != QMessageBox.StandardButton.Yes:
            logger.info("用户取消批量添加")
            return
        
        成功数量 = 0
        失败数量 = 0
        失败列表 = []
        
        # 遍历所有交易对
        for i in range(交易对数量):
            # 设置当前交易对
            self.comboBox_trading_pair.setCurrentIndex(i)
            交易对 = self.comboBox_trading_pair.currentText()
            
            # 收集表单数据
            机器人数据 = self.收集表单数据()
            if not 机器人数据:
                失败数量 += 1
                失败列表.append(交易对)
                logger.warning(f"收集表单数据失败: {交易对}")
                continue
            
            # 保存机器人数据
            if self.保存机器人数据(机器人数据):
                成功数量 += 1
                logger.info(f"成功添加策略: {交易对}")
            else:
                失败数量 += 1
                失败列表.append(交易对)
                logger.warning(f"添加策略失败: {交易对}")
        
        # 显示结果
        结果消息 = f"批量添加完成！成功：{成功数量}个，失败：{失败数量}个"
        logger.info(结果消息)
        
        # 如果有失败的交易对，显示警告信息框
        if 失败数量 > 0:
            详细信息 = f"批量添加完成！\n成功：{成功数量}个\n失败：{失败数量}个\n\n失败的交易对：\n" + "\n".join(失败列表)
            QMessageBox.warning(self, "批量添加结果", 详细信息)
        else:
            # 使用父窗口的状态栏显示成功消息
            if self.类_父窗口 and hasattr(self.类_父窗口, '类_状态栏管理'):
                self.类_父窗口.类_状态栏管理.显示系统提示(结果消息)
        
        # 如果全部添加成功，关闭窗口
        if 失败数量 == 0:
            self.accept()
        
        print(f"批量添加完成，成功：{成功数量}，失败：{失败数量}")
    
    def 收集表单数据(self) -> dict | None:
        """
        收集表单数据
        
        返回:
            机器人数据字典，如果验证失败返回None
        """
        # 获取基础数据
        交易对 = self.comboBox_trading_pair.currentText()
        
        # 验证交易对
        if not 交易对:
            QMessageBox.warning(self, "输入错误", "请选择交易对！")
            return None
        
        # 获取当前选择的策略类型
        当前标签页 = self.tabWidget_strategy.currentIndex()
        
        if 当前标签页 == 0:  # 自定义策略
            return self.收集自定义策略数据(交易对)
        elif 当前标签页 == 1:  # AI策略
            return self.收集AI策略数据(交易对)
        else:
            QMessageBox.warning(self, "输入错误", "请选择策略类型！")
            return None
    
    def 收集自定义策略数据(self, 参_交易对: str) -> dict | None:
        """
        收集自定义策略数据
        
        参数:
            参_交易对: 选择的交易对
            
        返回:
            自定义策略数据字典
        """
        # 获取自定义策略参数
        首单金额 = self.doubleSpinBox_first_amount.value()
        跌幅加仓 = self.doubleSpinBox_drop_percent.value()
        加仓倍数 = self.doubleSpinBox_position_multiplier.value()
        止盈比例 = self.doubleSpinBox_profit_percent.value()
        最大订单 = self.spinBox_max_orders.value()
        止盈金额 = self.doubleSpinBox_profit_stop.value()
        动态持仓 = self.checkBox_dynamic_position.isChecked()
        反弹补仓 = self.doubleSpinBox_rebound_percent.value()
        
        # 验证数据
        if 首单金额 <= 0:
            QMessageBox.warning(self, "输入错误", "首单金额必须大于0！")
            return None
        
        # 生成机器人编号
        机器人编号 = self.生成机器人编号()
        
        # 构建机器人数据
        机器人数据 = {
            "交易对": 参_交易对,
            "预配本金": 首单金额,  # 使用首单金额作为预配本金
            "预配首单": 首单金额,
            "本次下单": 0,
            "复利金额": 0,
            "累计下单": 0,
            "订单数量": 0,
            "最大订单": 最大订单,
            "浮动收益": 0,
            "浮动收益率": 0,
            "结算收益": 0,
            "持仓均价": 0,
            "持仓数量": 0,
            "预估平仓价格": 0,
            "预估月化率": 0,
            "预估年化率": 0,
            "策略状态": "停止",
            "当前状态": "待机",
            "策略配置": "自定义",
            "策略类型": "自定义",
            "子策略": "自定义",  # 自定义策略的子策略就是"自定义"
            "机器人编号": 机器人编号,
            # 保存策略参数到子策略中（JSON格式）
            "策略参数": {
                "首单金额": 首单金额,
                "跌幅加仓": 跌幅加仓,
                "加仓倍数": 加仓倍数,
                "止盈比例": 止盈比例,
                "最大订单": 最大订单,
                "止盈金额": 止盈金额,
                "动态持仓": 动态持仓,
                "反弹补仓": 反弹补仓
            }
        }
        
        return 机器人数据
    
    def 收集AI策略数据(self, 参_交易对: str) -> dict | None:
        """
        收集AI策略数据
        
        参数:
            参_交易对: 选择的交易对
            
        返回:
            AI策略数据字典
        """
        # 获取AI策略参数
        投入本金 = self.doubleSpinBox_capital.value()
        
        # 验证数据
        if 投入本金 <= 0:
            logger.warning("投入本金必须大于0")
            QMessageBox.warning(self, "输入错误", "投入本金必须大于0！")
            return None
        
        # 获取策略类型
        策略类型 = "通用型"  # 默认
        if self.radioButton_aggressive.isChecked():
            策略类型 = "进攻型"
        elif self.radioButton_defensive.isChecked():
            策略类型 = "防守型"
        elif self.radioButton_universal.isChecked():
            策略类型 = "通用型"
        
        # 获取子子策略（AI策略的子策略是子策略选择的结果）
        子策略 = "疯狂型"  # 默认
        if self.radioButton_crazy.isChecked():
            子策略 = "疯狂型"
        elif self.radioButton_aggressive_sub.isChecked():
            子策略 = "激进型"
        elif self.radioButton_balanced.isChecked():
            子策略 = "平衡型"
        elif self.radioButton_stable.isChecked():
            子策略 = "稳健型"
        elif self.radioButton_conservative.isChecked():
            子策略 = "保守型"
        elif self.radioButton_universal_a.isChecked():
            子策略 = "A"
        
        # 通过策略参数服务获取对应的详细参数配置
        AI策略参数 = None
        if hasattr(self, '类_策略参数服务') and self.类_策略参数服务:
            AI策略参数 = self.类_策略参数服务.获取策略参数(策略类型, 子策略)
        else:
            # 兼容模式：直接导入AI策略参数类
            try:
                from 模块类.功能类.类_AI策略参数 import 类_AI策略参数
                AI策略参数 = 类_AI策略参数.获取策略参数(策略类型, 子策略)
            except ImportError as e:
                logger.error(f"无法导入AI策略参数类: {e}", exc_info=True)
                print(f"无法导入AI策略参数类: {e}")
        
        if AI策略参数 is None:
            logger.warning(f"未找到策略类型 {策略类型} 下的子策略 {子策略} 参数配置")
            QMessageBox.warning(self, "参数错误", f"未找到策略类型 {策略类型} 下的子策略 {子策略} 参数配置！")
            return None
        
        # 生成机器人编号
        机器人编号 = self.生成机器人编号()
        # 计算预配首单,保留2位小数
        预配首单 = round(投入本金 / AI策略参数.get("复利分母", 0), 4)

        # 构建机器人数据
        机器人数据 = {
            "交易对": 参_交易对,
            "预配本金": 投入本金,
            "预配首单": 预配首单,  # AI策略的首单金额由算法决定
            "本次下单": 0,
            "复利金额": 0,
            "累计下单": 0,
            "订单数量": 0,
            "最大订单": AI策略参数.get("最大持仓单数", 0),  # 从AI策略参数中获取
            "浮动收益": 0,
            "浮动收益率": 0,
            "结算收益": 0,
            "持仓均价": 0,
            "持仓数量": 0,
            "预估平仓价格": 0,
            "预估月化率": 0,
            "预估年化率": 0,
            "策略状态": "停止",
            "当前状态": "待机",
            "策略配置": "AI策略",
            "策略类型": 策略类型,  # 进攻型、防守型、通用型
            "子策略": 子策略,  # AI策略的子策略选择结果
            "机器人编号": 机器人编号,
            # 保存完整的AI策略参数配置
            "策略参数": {
                "投入本金": 投入本金,
                "策略类型": 策略类型,
                "子策略": 子策略,
                # 添加从AI策略参数类中获取的所有详细参数
                **AI策略参数
            }
        }
        
        return 机器人数据
    
    def 生成机器人编号(self) -> str:
        """
        生成唯一的机器人编号
        
        返回:
            机器人编号字符串
        """
        import time
        import random
        
        # 使用时间戳和随机数生成唯一编号
        时间戳 = int(time.time())
        随机数 = random.randint(1000, 9999)
        机器人编号 = f"BOT_{时间戳}_{随机数}"
        
        return 机器人编号
    
    def 保存机器人数据(self, 参_机器人数据: dict) -> bool:
        """
        保存机器人数据到数据库
        
        参数:
            参_机器人数据: 机器人数据字典
            
        返回:
            保存是否成功
        """
        # 获取数据库管理器
        数据库管理器 = self.获取数据库管理器()
        
        if not 数据库管理器:
            logger.warning("无法获取数据库管理对象")
            QMessageBox.warning(self, "错误", "无法获取数据库连接")
            return False
        
        try:
            # 检查是否有策略参数JSON，如果有则转换为字符串
            if '策略参数JSON' in 参_机器人数据 and isinstance(参_机器人数据['策略参数JSON'], dict):
                try:
                    参_机器人数据['策略参数JSON'] = json.dumps(参_机器人数据['策略参数JSON'], ensure_ascii=False)
                except Exception as e:
                    logger.error(f"策略参数JSON序列化错误: {e}", exc_info=True)
                    参_机器人数据['策略参数JSON'] = "{}"
            
            # 保存机器人数据
            if 数据库管理器.添加机器人(参_机器人数据):
                logger.info(f"机器人数据保存成功: {参_机器人数据['机器人编号']}")
                return True
            else:
                logger.warning(f"机器人数据保存失败: {参_机器人数据['机器人编号']}")
                return False
                
        except KeyError as e:
            logger.error(f"机器人数据缺少必要字段: {e}", exc_info=True)
            QMessageBox.warning(self, "错误", f"机器人数据缺少必要字段: {e}")
            return False
            
        except TypeError as e:
            logger.error(f"数据库操作类型错误: {e}", exc_info=True)
            QMessageBox.warning(self, "错误", f"数据库操作类型错误: {e}")
            return False 