"""
表格列映射管理模块
负责管理所有表格的列映射关系，避免硬编码
"""

class 类_中控表格列:
    """
    中控表格列映射类
    管理中控表格的列索引映射关系，与数据库表结构保持一致
    """
    
    # 基本信息列
    列_序号 = 0
    列_交易对 = 1
    列_价格 = 2
    
    # 资金相关列
    列_预配本金 = 3
    列_预配首单 = 4
    列_本次下单 = 5
    列_复利金额 = 6
    列_累计下单 = 7
    
    # 订单相关列
    列_订单数量 = 8
    列_最大订单 = 9
    
    # 收益相关列
    列_浮动收益 = 10
    列_浮动收益率 = 11
    列_结算收益 = 12
    
    # 持仓相关列
    列_持仓均价 = 13
    列_持仓数量 = 14
    列_预估平仓价格 = 15
    
    # 收益率相关列
    列_预估月化率 = 16
    列_预估年化率 = 17
    
    # 策略相关列
    列_策略状态 = 18
    列_当前状态 = 19
    列_策略配置 = 20
    列_策略类型 = 21
    列_子策略 = 22
    列_创建时间 = 23
    列_机器人编号 = 24

    @classmethod
    def 获取列说明(cls) -> dict:
        """
        获取所有列的说明信息
        
        @return dict: 列索引与说明的映射字典
        """
        return {
            cls.列_序号: "序号",
            cls.列_交易对: "交易对",
            cls.列_价格: "价格",
            cls.列_预配本金: "预配本金",
            cls.列_预配首单: "预配首单",
            cls.列_本次下单: "本次下单",
            cls.列_复利金额: "复利金额",
            cls.列_累计下单: "累计下单",
            cls.列_订单数量: "订单数量",
            cls.列_最大订单: "最大订单",
            cls.列_浮动收益: "浮动收益",
            cls.列_浮动收益率: "浮动收益率",
            cls.列_结算收益: "结算收益",
            cls.列_持仓均价: "持仓均价",
            cls.列_持仓数量: "持仓数量",
            cls.列_预估平仓价格: "预估平仓价格",
            cls.列_预估月化率: "预估月化率",
            cls.列_预估年化率: "预估年化率",
            cls.列_策略状态: "策略状态",
            cls.列_当前状态: "当前状态",
            cls.列_策略配置: "策略配置",
            cls.列_策略类型: "策略类型",
            cls.列_子策略: "子策略",
            cls.列_创建时间: "创建时间",
            cls.列_机器人编号: "机器人编号"
        }

class 类_持仓表格列:
    """
    持仓表格列映射类
    管理持仓表格的列索引映射关系
    """
    
    # 基本信息列
    列_序号 = 0
    列_交易对 = 1
    列_机器人编号 = 2
    列_创建时间 = 3
    
    # 开仓信息列
    列_开仓蜡烛时间 = 4
    列_开仓时间 = 5
    列_开仓金额 = 6
    列_开仓数量 = 7
    列_开仓手续费 = 8
    
    # 策略信息列
    列_策略配置 = 9
    列_策略类型 = 10
    列_子策略 = 11

    @classmethod
    def 获取列说明(cls) -> dict:
        """
        获取所有列的说明信息
        
        @return dict: 列索引与说明的映射字典
        """
        return {
            cls.列_序号: "序号",
            cls.列_交易对: "交易对",
            cls.列_机器人编号: "机器人编号",
            cls.列_创建时间: "创建时间",
            cls.列_开仓蜡烛时间: "开仓蜡烛时间",
            cls.列_开仓时间: "开仓时间",
            cls.列_开仓金额: "开仓金额",
            cls.列_开仓数量: "开仓数量",
            cls.列_开仓手续费: "开仓手续费",
            cls.列_策略配置: "策略配置",
            cls.列_策略类型: "策略类型",
            cls.列_子策略: "子策略"
        }

class 类_平仓表格列:
    """
    平仓表格列映射类
    管理平仓表格的列索引映射关系
    """
    
    # 基本信息列
    列_序号 = 0
    列_交易对 = 1
    列_机器人编号 = 2
    列_创建时间 = 3
    
    # 开仓信息列
    列_开仓蜡烛时间 = 4
    列_开仓时间 = 5
    列_开仓金额 = 6
    列_开仓数量 = 7
    列_开仓手续费 = 8
    
    # 平仓信息列
    列_平仓蜡烛时间 = 9
    列_平仓时间 = 10
    列_平仓金额 = 11
    列_平仓数量 = 12
    列_平仓收益 = 13
    列_平仓收益率 = 14
    列_平仓手续费 = 15
    
    # 策略信息列
    列_策略配置 = 16
    列_策略类型 = 17
    列_子策略 = 18

    @classmethod
    def 获取列说明(cls) -> dict:
        """
        获取所有列的说明信息
        
        @return dict: 列索引与说明的映射字典
        """
        return {
            cls.列_序号: "序号",
            cls.列_交易对: "交易对",
            cls.列_机器人编号: "机器人编号",
            cls.列_创建时间: "创建时间",
            cls.列_开仓蜡烛时间: "开仓蜡烛时间",
            cls.列_开仓时间: "开仓时间",
            cls.列_开仓金额: "开仓金额",
            cls.列_开仓数量: "开仓数量",
            cls.列_开仓手续费: "开仓手续费",
            cls.列_平仓蜡烛时间: "平仓蜡烛时间",
            cls.列_平仓时间: "平仓时间",
            cls.列_平仓金额: "平仓金额",
            cls.列_平仓数量: "平仓数量",
            cls.列_平仓收益: "平仓收益",
            cls.列_平仓收益率: "平仓收益率",
            cls.列_平仓手续费: "平仓手续费",
            cls.列_策略配置: "策略配置",
            cls.列_策略类型: "策略类型",
            cls.列_子策略: "子策略"
        }

class 类_历史数据表格列:
    """
    历史数据表格列映射类（对应数据库平仓表）
    管理历史数据表格的列索引映射关系
    """
    
    # 基本信息列
    列_序号 = 0
    列_交易对 = 1
    列_机器人编号 = 18
    列_创建时间 = 17
    
    # 开仓信息列
    列_开仓蜡烛时间 = 2
    列_开仓时间 = 3
    列_开仓金额 = 4
    列_开仓数量 = 5
    列_开仓手续费 = 6
    
    # 平仓信息列
    列_平仓蜡烛时间 = 7
    列_平仓时间 = 8
    列_平仓金额 = 9
    列_平仓数量 = 10
    列_平仓收益 = 11
    列_平仓收益率 = 12
    列_平仓手续费 = 13
    
    # 策略信息列
    列_策略配置 = 14
    列_策略类型 = 15
    列_子策略 = 16
    
    @classmethod
    def 获取列说明(cls) -> dict:
        """
        获取所有列的说明信息
        
        @return dict: 列索引与说明的映射字典
        """
        return {
            cls.列_序号: "序号",
            cls.列_交易对: "交易对",
            cls.列_开仓蜡烛时间: "开仓蜡烛时间",
            cls.列_开仓时间: "开仓时间",
            cls.列_开仓金额: "开仓金额",
            cls.列_开仓数量: "开仓数量",
            cls.列_开仓手续费: "开仓手续费",
            cls.列_平仓蜡烛时间: "平仓蜡烛时间",
            cls.列_平仓时间: "平仓时间",
            cls.列_平仓金额: "平仓金额",
            cls.列_平仓数量: "平仓数量",
            cls.列_平仓收益: "平仓收益",
            cls.列_平仓收益率: "平仓收益率",
            cls.列_平仓手续费: "平仓手续费",
            cls.列_策略配置: "策略配置",
            cls.列_策略类型: "策略类型",
            cls.列_子策略: "子策略",
            cls.列_创建时间: "创建时间",
            cls.列_机器人编号: "机器人编号"
        }

class 类_日志表格列:
    """
    日志表格列映射类
    管理日志表格的列索引映射关系
    """
    
    # 基本信息列
    列_序号 = 0
    列_交易对 = 1
    列_机器人编号 = 2
    列_订单类型 = 3
    
    # 时间信息列
    列_蜡烛时间 = 4
    列_开平仓时间 = 5
    
    # 交易信息列
    列_金额 = 6
    列_数量 = 7
    列_手续费 = 8
    列_收益收益率 = 9
    
    # 策略信息列
    列_策略配置 = 10
    列_策略类型 = 11
    列_子策略 = 12
    
    @classmethod
    def 获取列说明(cls) -> dict:
        """
        获取所有列的说明信息
        
        @return dict: 列索引与说明的映射字典
        """
        return {
            cls.列_序号: "序号",
            cls.列_交易对: "交易对",
            cls.列_机器人编号: "机器人编号",
            cls.列_订单类型: "订单类型",
            cls.列_蜡烛时间: "蜡烛时间",
            cls.列_开平仓时间: "开平仓时间",
            cls.列_金额: "金额",
            cls.列_数量: "数量",
            cls.列_手续费: "手续费",
            cls.列_收益收益率: "收益/收益率",
            cls.列_策略配置: "策略配置",
            cls.列_策略类型: "策略类型",
            cls.列_子策略: "子策略"
        }

def 获取列索引(cls, 列名: str) -> int:
    """
    根据列名获取列索引
    
    @param cls: 表格列映射类
    @param 列名 (str): 列的名称
    @return int: 列的索引值
    @exception ValueError: 当列名不存在时抛出
    """
    列说明字典 = cls.获取列说明()
    for 索引, 说明 in 列说明字典.items():
        if 说明 == 列名:
            return 索引
    raise ValueError(f"列名 {列名} 不存在")

def 获取列数量(cls) -> int:
    """
    获取表格的总列数
    
    @param cls: 表格列映射类
    @return int: 表格的总列数
    """
    return len(cls.获取列说明()) 