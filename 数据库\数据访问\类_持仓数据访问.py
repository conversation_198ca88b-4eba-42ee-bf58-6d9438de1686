"""
持仓数据访问类
负责持仓表的数据访问操作
"""

import logging
import sqlite3
from typing import List, Dict, Any, Optional
from .类_数据库连接管理 import 类_数据库连接管理

# 获取当前模块的日志记录器
logger = logging.getLogger(__name__)

class 类_持仓数据访问:
    """
    持仓数据访问类
    负责持仓表的数据访问操作
    """
    
    def __init__(self, 参_数据库连接管理: Optional[类_数据库连接管理] = None):
        """
        初始化持仓数据访问类
        
        @param 参_数据库连接管理 (Optional[类_数据库连接管理]): 数据库连接管理实例
        @return None
        @exception Exception: 初始化过程中可能发生的未知错误
        """
        # 步骤1: 保存数据库连接管理引用
        # 如果外部传入了数据库连接管理，则使用该实例
        # 否则创建新的实例
        self.类_数据库连接管理 = 参_数据库连接管理 or 类_数据库连接管理()
        
        # 步骤2: 记录初始化信息
        # 记录持仓数据访问类初始化的日志
        logger.info("持仓数据访问类初始化完成")
    
    def 添加持仓记录(self, 参_持仓数据: Dict[str, Any]) -> bool:
        """
        添加持仓记录
        
        @param 参_持仓数据 (Dict[str, Any]): 持仓数据字典
        @return bool: 添加是否成功
        @exception Exception: 添加过程中可能发生的未知错误
        """
        # 步骤1: 获取数据库连接
        # 通过数据库连接管理获取数据库连接
        连接 = self.类_数据库连接管理.获取连接()
        游标 = 连接.cursor()
        
        try:
            # 步骤2: 构建SQL语句
            # 构建插入持仓表的SQL语句
            插入SQL = """
            INSERT INTO 持仓表 (
                交易对, 开仓蜡烛时间, 开仓时间, 开仓金额, 开仓数量, 
                开仓手续费, 策略配置, 策略类型, 子策略, 机器人编号
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            # 步骤3: 准备参数
            # 从持仓数据字典中获取参数值
            参数 = (
                参_持仓数据.get('交易对'),
                参_持仓数据.get('开仓蜡烛时间'),
                参_持仓数据.get('开仓时间'),
                参_持仓数据.get('开仓金额'),
                参_持仓数据.get('开仓数量'),
                参_持仓数据.get('开仓手续费', 0),
                参_持仓数据.get('策略配置', '自定义'),
                参_持仓数据.get('策略类型', '通用型'),
                参_持仓数据.get('子策略', '默认策略'),
                参_持仓数据.get('机器人编号')
            )
            
            # 步骤4: 执行SQL
            # 执行插入操作
            游标.execute(插入SQL, 参数)
            连接.commit()
            
            # 步骤5: 记录成功日志
            # 记录添加持仓记录成功的日志
            logger.info(f"添加持仓记录成功: 机器人编号={参_持仓数据.get('机器人编号')}, 交易对={参_持仓数据.get('交易对')}")
            return True
            
        except Exception as e:
            # 步骤6: 异常处理
            # 如果添加过程中出现异常，回滚事务并记录错误日志
            连接.rollback()
            logger.error(f"添加持仓记录失败: {e}", exc_info=True)
            return False
    
    def 获取持仓记录(self, 参_机器人编号: str, 参_交易对: str) -> List[Dict[str, Any]]:
        """
        获取指定机器人和交易对的持仓记录
        
        @param 参_机器人编号 (str): 机器人编号
        @param 参_交易对 (str): 交易对名称
        @return List[Dict[str, Any]]: 持仓记录列表
        @exception Exception: 获取过程中可能发生的未知错误
        """
        # 步骤1: 获取数据库连接
        # 通过数据库连接管理获取数据库连接
        连接 = self.类_数据库连接管理.获取连接()
        游标 = 连接.cursor()
        
        try:
            # 步骤2: 构建SQL语句
            # 构建查询持仓表的SQL语句
            查询SQL = """
            SELECT * FROM 持仓表
            WHERE 机器人编号 = ? AND 交易对 = ?
            ORDER BY 开仓时间 DESC
            """
            
            # 步骤3: 执行SQL
            # 执行查询操作
            游标.execute(查询SQL, (参_机器人编号, 参_交易对))
            持仓记录 = 游标.fetchall()
            
            # 步骤4: 处理查询结果
            # 如果没有找到持仓记录，返回空列表
            if not 持仓记录:
                logger.info(f"未找到持仓记录: 机器人编号={参_机器人编号}, 交易对={参_交易对}")
                return []
            
            # 步骤5: 获取列名
            # 获取查询结果的列名
            列名 = [description[0] for description in 游标.description]
            
            # 步骤6: 转换为字典列表
            # 将查询结果转换为字典列表
            持仓列表 = []
            for 记录 in 持仓记录:
                持仓字典 = {}
                for i, 值 in enumerate(记录):
                    持仓字典[列名[i]] = 值
                持仓列表.append(持仓字典)
            
            # 步骤7: 记录成功日志
            # 记录获取持仓记录成功的日志
            logger.info(f"获取持仓记录成功: 机器人编号={参_机器人编号}, 交易对={参_交易对}, 记录数={len(持仓列表)}")
            return 持仓列表
            
        except Exception as e:
            # 步骤8: 异常处理
            # 如果获取过程中出现异常，记录错误日志
            logger.error(f"获取持仓记录失败: {e}", exc_info=True)
            return []
    
    def 获取持仓信息(self, 参_机器人编号: str, 参_交易对: str) -> Optional[Dict[str, Any]]:
        """
        获取持仓信息，包括持仓记录列表、持仓均价、持仓数量等
        
        @param 参_机器人编号 (str): 机器人编号
        @param 参_交易对 (str): 交易对名称
        @return Optional[Dict[str, Any]]: 持仓信息，如果未找到则返回None
        @exception Exception: 获取过程中可能发生的未知错误
        """
        try:
            # 步骤1: 获取持仓记录列表
            # 调用获取持仓记录方法获取持仓记录列表
            持仓列表 = self.获取持仓记录(参_机器人编号, 参_交易对)
            
            # 步骤2: 检查持仓列表
            # 如果持仓列表为空，返回None
            if not 持仓列表:
                return None
            
            # 步骤3: 计算持仓总量和均价
            # 计算总开仓金额和总数量
            总开仓金额 = sum(持仓["开仓金额"] for 持仓 in 持仓列表)
            总数量 = sum(持仓["开仓数量"] for 持仓 in 持仓列表)
            
            # 计算持仓均价
            持仓均价 = 总开仓金额 / 总数量 if 总数量 > 0 else 0
            
            # 步骤4: 构建返回结果
            # 构建包含持仓信息的字典
            持仓信息 = {
                "持仓列表": 持仓列表,
                "持仓订单数": len(持仓列表),
                "持仓均价": 持仓均价,
                "持仓数量": 总数量,
                "开仓总金额": 总开仓金额
            }
            
            # 步骤5: 记录成功日志
            # 记录获取持仓信息成功的日志
            logger.info(f"获取持仓信息成功: 机器人编号={参_机器人编号}, 交易对={参_交易对}, 持仓数量={总数量}")
            return 持仓信息
            
        except Exception as e:
            # 步骤6: 异常处理
            # 如果获取过程中出现异常，记录错误日志
            logger.error(f"获取持仓信息失败: {e}", exc_info=True)
            return None
    
    def 删除持仓记录(self, 参_序号: int) -> bool:
        """
        删除指定序号的持仓记录
        
        @param 参_序号 (int): 持仓记录序号
        @return bool: 删除是否成功
        @exception Exception: 删除过程中可能发生的未知错误
        """
        # 步骤1: 获取数据库连接
        # 通过数据库连接管理获取数据库连接
        连接 = self.类_数据库连接管理.获取连接()
        游标 = 连接.cursor()
        
        try:
            # 步骤2: 构建SQL语句
            # 构建删除持仓记录的SQL语句
            删除SQL = "DELETE FROM 持仓表 WHERE 序号 = ?"
            
            # 步骤3: 执行SQL
            # 执行删除操作
            游标.execute(删除SQL, (参_序号,))
            连接.commit()
            
            # 步骤4: 检查影响行数
            # 如果影响行数为0，表示没有找到对应的记录
            if 游标.rowcount == 0:
                logger.warning(f"未找到要删除的持仓记录: 序号={参_序号}")
                return False
            
            # 步骤5: 记录成功日志
            # 记录删除持仓记录成功的日志
            logger.info(f"删除持仓记录成功: 序号={参_序号}")
            return True
            
        except Exception as e:
            # 步骤6: 异常处理
            # 如果删除过程中出现异常，回滚事务并记录错误日志
            连接.rollback()
            logger.error(f"删除持仓记录失败: {e}", exc_info=True)
            return False
    
    def 清空持仓记录(self, 参_机器人编号: str, 参_交易对: Optional[str] = None) -> bool:
        """
        清空指定机器人的持仓记录，可选择指定交易对
        
        @param 参_机器人编号 (str): 机器人编号
        @param 参_交易对 (Optional[str]): 交易对名称，如果为None则清空所有交易对的持仓记录
        @return bool: 清空是否成功
        @exception Exception: 清空过程中可能发生的未知错误
        """
        # 步骤1: 获取数据库连接
        # 通过数据库连接管理获取数据库连接
        连接 = self.类_数据库连接管理.获取连接()
        游标 = 连接.cursor()
        
        try:
            # 步骤2: 构建SQL语句
            # 根据是否指定交易对构建不同的SQL语句
            if 参_交易对:
                清空SQL = "DELETE FROM 持仓表 WHERE 机器人编号 = ? AND 交易对 = ?"
                参数 = (参_机器人编号, 参_交易对)
            else:
                清空SQL = "DELETE FROM 持仓表 WHERE 机器人编号 = ?"
                参数 = (参_机器人编号,)
            
            # 步骤3: 执行SQL
            # 执行清空操作
            游标.execute(清空SQL, 参数)
            连接.commit()
            
            # 步骤4: 记录成功日志
            # 记录清空持仓记录成功的日志
            if 参_交易对:
                logger.info(f"清空持仓记录成功: 机器人编号={参_机器人编号}, 交易对={参_交易对}")
            else:
                logger.info(f"清空持仓记录成功: 机器人编号={参_机器人编号}, 所有交易对")
            return True
            
        except Exception as e:
            # 步骤5: 异常处理
            # 如果清空过程中出现异常，回滚事务并记录错误日志
            连接.rollback()
            logger.error(f"清空持仓记录失败: {e}", exc_info=True)
            return False 