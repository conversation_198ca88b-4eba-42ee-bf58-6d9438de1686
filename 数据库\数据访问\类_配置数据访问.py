"""
配置数据访问类
负责处理系统配置和API配置的数据库操作
"""

from typing import Dict, Any, Optional
import logging
from .类_数据库连接管理 import 类_数据库连接管理

# 获取当前模块的日志记录器
logger = logging.getLogger(__name__)

class 类_配置数据访问:
    """
    配置数据访问类
    负责处理系统配置和API配置的数据库操作
    """
    
    def __init__(self, 参_数据库连接管理: Optional[类_数据库连接管理] = None):
        """
        初始化配置数据访问类
        
        参数:
            参_数据库连接管理: 数据库连接管理实例
        """
        self.类_数据库连接管理 = 参_数据库连接管理 or 类_数据库连接管理()
        logger.info("配置数据访问类初始化完成")
    
    def 保存API配置(self, 参_交易所名称: str, 参_API密钥: str, 参_API密码: str, 参_API密码短语: Optional[str] = None) -> bool:
        """
        保存API配置
        
        参数:
            参_交易所名称: 交易所名称
            参_API密钥: API密钥
            参_API密码: API密码
            参_API密码短语: API密码短语（OKX需要）
            
        返回:
            操作是否成功
        """
        连接 = self.类_数据库连接管理.获取连接()
        游标 = 连接.cursor()
        
        try:
            插入SQL = """
            INSERT OR REPLACE INTO API配置表 
            (交易所名称, api_key, secret_key, passphrase) 
            VALUES (?, ?, ?, ?)
            """
            
            游标.execute(插入SQL, (参_交易所名称, 参_API密钥, 参_API密码, 参_API密码短语))
            连接.commit()
            
            logger.info(f"API配置保存成功: {参_交易所名称}")
            return True
            
        except Exception as e:
            连接.rollback()
            logger.error(f"API配置保存失败: {参_交易所名称}, 错误: {e}", exc_info=True)
            return False
    
    def 获取API配置(self, 参_交易所名称: str) -> Optional[Dict[str, str]]:
        """
        获取API配置
        
        参数:
            参_交易所名称: 交易所名称
            
        返回:
            API配置字典，如果未找到则返回None
        """
        连接 = self.类_数据库连接管理.获取连接()
        游标 = 连接.cursor()
        
        try:
            查询SQL = "SELECT * FROM API配置表 WHERE 交易所名称 = ?"
            游标.execute(查询SQL, (参_交易所名称,))
            结果 = 游标.fetchone()
            
            if 结果:
                logger.info(f"获取API配置成功: {参_交易所名称}")
                return dict(结果)
            else:
                logger.warning(f"未找到交易所API配置: {参_交易所名称}")
                return None
                
        except Exception as e:
            logger.error(f"获取API配置失败: {参_交易所名称}, 错误: {e}", exc_info=True)
            return None
    
    def 保存系统配置(self, 参_配置键: str, 参_配置值: str) -> bool:
        """
        保存系统配置
        
        参数:
            参_配置键: 配置键
            参_配置值: 配置值
            
        返回:
            操作是否成功
        """
        连接 = self.类_数据库连接管理.获取连接()
        游标 = 连接.cursor()
        
        try:
            插入SQL = """
            INSERT OR REPLACE INTO 系统配置表 
            (配置键, 配置值) 
            VALUES (?, ?)
            """
            
            游标.execute(插入SQL, (参_配置键, 参_配置值))
            连接.commit()
            
            logger.info(f"系统配置保存成功: {参_配置键}")
            return True
            
        except Exception as e:
            连接.rollback()
            logger.error(f"系统配置保存失败: {参_配置键}, 错误: {e}", exc_info=True)
            return False
    
    def 获取系统配置(self, 参_配置键: str) -> Optional[str]:
        """
        获取系统配置
        
        参数:
            参_配置键: 配置键
            
        返回:
            配置值，如果未找到则返回None
        """
        连接 = self.类_数据库连接管理.获取连接()
        游标 = 连接.cursor()
        
        try:
            查询SQL = "SELECT 配置值 FROM 系统配置表 WHERE 配置键 = ?"
            游标.execute(查询SQL, (参_配置键,))
            结果 = 游标.fetchone()
            
            if 结果:
                logger.info(f"获取系统配置成功: {参_配置键}")
                return 结果['配置值']
            else:
                logger.warning(f"未找到系统配置: {参_配置键}")
                return None
                
        except Exception as e:
            logger.error(f"获取系统配置失败: {参_配置键}, 错误: {e}", exc_info=True)
            return None 