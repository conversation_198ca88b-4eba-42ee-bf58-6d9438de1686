"""
数据库连接管理类
负责数据库连接的创建、维护和关闭
"""

import sqlite3
import os
from typing import Optional

class 类_数据库连接管理:
    """
    数据库连接管理类
    负责处理数据库连接的创建、维护和关闭
    """
    
    def __init__(self, 参_数据库路径: Optional[str] = None):
        """
        初始化数据库连接管理类
        
        参数:
            参_数据库路径: 数据库文件路径，如果为None则使用默认路径
        """
        if 参_数据库路径 is None:
            # 获取项目根目录
            当前目录 = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            项目根目录 = os.path.dirname(当前目录)
            self.类_数据库路径 = os.path.join(项目根目录, "数据库", "量化交易数据.db")
        else:
            self.类_数据库路径 = 参_数据库路径
            
        self.类_连接 = None
        
        # 确保数据库目录存在
        数据库目录 = os.path.dirname(self.类_数据库路径)
        if 数据库目录 and not os.path.exists(数据库目录):
            os.makedirs(数据库目录)
        
        print(f"数据库连接管理类初始化完成，数据库路径: {self.类_数据库路径}")
    
    def 获取连接(self) -> sqlite3.Connection:
        """
        获取数据库连接
        
        返回:
            数据库连接对象
        """
        if self.类_连接 is None:
            self.类_连接 = sqlite3.connect(self.类_数据库路径)
            # 设置行工厂，使查询结果以字典形式返回
            self.类_连接.row_factory = sqlite3.Row
        return self.类_连接
    
    def 关闭连接(self):
        """
        关闭数据库连接
        """
        if self.类_连接:
            self.类_连接.close()
            self.类_连接 = None
    
    def __del__(self):
        """
        析构函数，确保数据库连接关闭
        """
        self.关闭连接() 