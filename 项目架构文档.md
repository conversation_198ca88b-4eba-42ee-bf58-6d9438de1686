# 量化交易系统 - 项目架构文档

## 📋 项目概述

这是一个基于Python和PySide6开发的现货量化交易系统，采用模块化设计，支持多交易所、多策略的自动化交易。

## 🏗️ 项目结构总览

```
Quantification/                    # 项目根目录
├── main.py                       # 程序入口文件
├── logs/                         # 日志目录
├── 数据库/                       # 数据库模块
├── 模块类/                       # 核心功能模块
├── 窗口UI布局/                   # 用户界面模块
├── 测试/                         # 测试文件目录
├── 配置/                         # 配置文件目录
└── 项目架构文档.md               # 项目架构文档
```

## 📁 详细目录结构

### 🚀 程序入口
- **`main.py`** - 程序主入口，负责应用程序启动和日志系统初始化

### 🗄️ 数据库模块 (`数据库/`)
```
数据库/
├── 数据库管理.py                 # 数据库管理主类，统一数据库操作接口
├── 量化交易数据.db              # SQLite数据库文件
└── 数据访问/                    # 数据访问层
    ├── __init__.py
    ├── 类_数据库连接管理.py      # 数据库连接管理
    ├── 类_表结构管理.py          # 数据表结构管理
    ├── 类_机器人数据访问.py      # 机器人配置数据访问
    ├── 类_配置数据访问.py        # 系统配置数据访问
    ├── 类_交易对数据访问.py      # 交易对配置数据访问
    ├── 类_持仓数据访问.py        # 持仓数据访问
    ├── 类_表格列映射管理.py      # 表格列映射管理
    └── 类_开平仓日志数据访问.py  # 交易日志数据访问
```

**功能说明：**
- `数据库管理.py`: 提供统一的数据库操作接口，管理所有数据访问类
- `数据访问/`: 采用DAO模式，每个类负责特定表的数据操作
- 支持的数据表：中控表、持仓表、平仓表、API配置表、交易对配置表等

### 🔧 核心功能模块 (`模块类/`)
```
模块类/
├── 交易所API接口/               # 交易所接口模块
│   ├── OKX/                     # OKX交易所实现
│   │   └── 类_OKX接口.py        # OKX交易所API封装
│   └── 币安/                    # 币安交易所实现（待开发）
├── 指标类/                      # 技术指标计算模块
│   ├── __init__.py
│   ├── 类_指标计算.py           # 技术指标计算（CCI、ATR等）
│   └── 类_指标信号计算.py       # 指标信号分析
└── 功能类/                      # 通用功能模块
    ├── 类_加密工具.py           # 数据加密解密工具
    ├── 类_线程管理.py           # 多线程交易管理
    ├── 类_核心计算.py           # 核心计算逻辑
    ├── 类_其它功能.py           # 其他辅助功能
    └── 类_AI策略参数.py         # AI策略参数管理
```

**功能说明：**
- `交易所API接口/`: 封装各交易所API，提供统一的交易接口
- `指标类/`: 实现常用技术指标计算和交易信号生成
- `功能类/`: 提供加密、线程管理、核心计算等通用功能

### 🖥️ 用户界面模块 (`窗口UI布局/`)
```
窗口UI布局/
├── UI文件/                      # UI设计文件
│   ├── __init__.py
│   ├── Main_window.ui           # 主窗口UI设计文件
│   ├── Main_window_ui.py        # 主窗口UI Python类
│   ├── Strategy_Config.ui       # 策略配置窗口UI
│   ├── Strategy_Config_ui.py    # 策略配置窗口Python类
│   ├── Strategy_Edit.ui         # 策略编辑窗口UI
│   └── Strategy_Edit_ui.py      # 策略编辑窗口Python类
└── UI管理/                      # UI逻辑管理
    ├── 类_主窗口.py             # 主窗口逻辑控制器
    ├── 主窗口模块/              # 主窗口功能模块
    │   ├── __init__.py
    │   ├── 类_UI初始化管理.py   # UI初始化管理
    │   ├── 类_信号槽管理.py     # 信号槽连接管理
    │   ├── 类_表格数据管理.py   # 表格数据管理
    │   ├── 类_API配置管理.py    # API配置管理
    │   ├── 类_策略控制管理.py   # 策略控制管理
    │   ├── 类_状态栏管理.py     # 状态栏管理
    │   └── 类_线程安全UI更新器.py # 线程安全的UI更新管理
    └── 策略窗口模块/            # 策略窗口相关模块
        ├── __init__.py
        ├── 类_策略配置窗口.py   # 策略配置窗口逻辑
        └── 类_策略编辑窗口.py   # 策略编辑窗口逻辑
```

**功能说明：**
- `UI文件/`: 使用Qt Designer设计的UI文件及其Python转换文件
- `UI管理/`: 采用MVC模式，分离UI逻辑和业务逻辑
- 主窗口采用模块化设计，每个功能模块独立管理
- 新增线程安全UI更新器，确保多线程环境下UI更新的安全性

### 🧪 测试模块 (`测试/`)
```
测试/
├── 测试.py                      # 基础测试文件
├── 测试_简单线程.py             # 线程功能测试文件
├── 测试_完整跨线程更新.py       # 完整的跨线程UI更新测试
├── 测试_跨线程UI更新.py         # 跨线程UI更新测试
└── 诊断_UI更新器传递.py         # UI更新器传递诊断工具
```

**功能说明：**
- 基础功能测试
- 线程管理测试
- UI更新机制测试
- 跨线程通信测试
- 性能诊断工具

### 📝 配置和日志
```
logs/                            # 日志目录
├── 系统日志.log.daily           # 当前日志文件
└── 系统日志.log.daily.2025-07-13 # 历史日志文件

配置/                           # 配置文件目录
└── 系统配置文件                # 系统配置存储
```

## 🔄 系统架构流程

### 1. 程序启动流程
```
main.py → 初始化日志系统 → 创建主窗口 → 启动Qt应用程序
```

### 2. 数据流向
```
UI界面 ↔ 线程安全UI更新器 ↔ 主窗口逻辑 ↔ 数据库管理 ↔ 数据访问层 ↔ SQLite数据库
```

### 3. 交易流程
```
策略配置 → 线程管理 → 交易所API → 指标计算 → 信号生成 → 交易执行 → 日志记录
```

## 🎯 核心功能模块说明

### 数据库管理
- **统一接口**: `类_数据库管理`提供所有数据操作的统一入口
- **连接管理**: 自动管理SQLite连接的创建和关闭
- **表结构管理**: 自动创建和更新数据库表结构
- **数据访问**: 每个业务实体都有对应的数据访问类

### 交易管理
- **多交易所支持**: 目前支持OKX交易所，币安交易所待开发
- **统一接口**: 不同交易所使用相同的接口规范
- **线程管理**: 支持多交易对并发交易
- **策略管理**: 支持多种交易策略配置

### 用户界面
- **模块化设计**: 主窗口功能按模块划分，便于维护
- **响应式布局**: 使用Qt的布局管理器实现自适应界面
- **实时数据**: 表格数据实时更新，显示交易状态
- **线程安全**: 通过UI更新器确保多线程环境下的UI安全更新

## 🔧 技术栈

- **界面框架**: PySide6 (Qt6)
- **数据库**: SQLite3
- **加密**: AES-256-CBC
- **日志**: Python logging模块
- **多线程**: Python threading模块
- **开发工具**: Qt Designer (UI设计)

## 📖 使用说明

1. **启动程序**: 运行 `python main.py`
2. **配置API**: 在主界面配置交易所API密钥
3. **添加交易对**: 配置要交易的币种对
4. **设置策略**: 配置交易策略参数
5. **启动交易**: 通过右键菜单启动交易线程

## 🚀 扩展指南

### 添加新交易所
1. 在 `模块类/交易所API接口/` 下创建新的交易所目录
2. 实现统一的交易接口
3. 在UI中添加交易所选择选项

### 添加新指标
1. 在 `类_指标计算.py` 中添加指标计算方法
2. 在 `类_指标信号计算.py` 中添加信号生成逻辑
3. 在策略配置中添加参数设置

### 添加新策略
1. 在 `类_AI策略参数.py` 中定义策略参数
2. 在线程管理中实现策略逻辑
3. 在UI中添加策略配置界面

## 📊 项目状态

### ✅ 已完成功能
- 基础UI界面框架
- 数据库管理系统
- OKX交易所API接口
- 基础技术指标计算
- 多线程交易管理
- 加密工具模块
- 线程安全UI更新机制
- 跨线程通信系统

### 🚧 开发中功能
- 币安交易所API接口
- 高级策略算法
- 回测系统
- 性能优化

### 📋 待开发功能
- 风险管理模块
- 性能分析报告
- 移动端适配
- 实时监控系统

---

*本文档会随着项目发展持续更新*
