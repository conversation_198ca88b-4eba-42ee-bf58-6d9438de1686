import logging
# 获取当前模块的日志记录器
logger = logging.getLogger(__name__)
class 类_指标信号计算:
    """
    指标信号计算类
    负责基于技术指标计算交易信号
    """

    def __init__(self):
        """
        初始化指标信号计算类
        """
        print("指标信号计算类初始化完成")

    def 比较ATR信号(self, 参_ATR列表):
        """
        比较ATR列表索引0和1的值，返回最大值
        
        参数:
            参_ATR列表: ATR指标计算后的列表，索引0为最新ATR值
            
        返回:
            索引0和1中较大的ATR值，如果数据不足则返回None
        """
        try:
            # 检查ATR列表长度是否足够
            if len(参_ATR列表) < 2:
                logger.error("错误：ATR列表长度不足，需要至少2个数据点")
                return None   
            # 比较两个值，返回最大值
            最大值 = max(参_ATR列表[0], 参_ATR列表[1])
            logger.info(f"ATR信号比较完成：最新ATR={参_ATR列表[0]:.4f}，前一个ATR={参_ATR列表[1]:.4f}，最大值={最大值:.4f}")
            return 最大值
            
        except Exception as e:
            logger.error(f"比较ATR信号时发生错误: {e}")
            return None
    def 获取CCI信号(self, 参_CCI列表, 参_CCI阈值=0 ,参_CCI确认蜡烛数量=1) -> bool:
        """
        获取CCI信号
        
        参数:
            参_CCI列表: CCI指标计算后的列表，索引0为最新CCI值
            参_CCI阈值: CCI阈值，默认0
            参_CCI确认蜡烛数量: CCI确认蜡烛数量，默认1
        返回:
            True: 如果所有指定的蜡烛都大于CCI阈值
            False: 如果至少有一个指定的蜡烛小于或等于CCI阈值
        """
        try:
            # 检查CCI列表长度是否足够
            if len(参_CCI列表) < 参_CCI确认蜡烛数量:
                logger.error(f"错误：CCI列表长度不足，需要至少{参_CCI确认蜡烛数量}个数据点")
                return False
            # 获取CCI信号
            for i in range(参_CCI确认蜡烛数量):
                if 参_CCI列表[i] <= 参_CCI阈值:
                    return False
            return True
            #其它的方式:return all(参_CCI列表[i] > 参_CCI阈值 for i in range(参_CCI确认蜡烛数量))
        except Exception as e:
            logger.error(f"获取CCI信号时发生错误: {e}")
            return False


