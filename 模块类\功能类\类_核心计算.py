# -*- coding: utf-8 -*-
"""
主要的计算功能类
包含交易逻辑处理、策略执行等功能
"""

import logging
import json
from typing import Dict, Any, Optional, List, Tuple

from .类_AI策略参数 import 类_AI策略参数
from ..交易所API接口.OKX.类_OKX接口 import 类_OKX


# 获取当前模块的日志记录器
logger = logging.getLogger(__name__)

class 类_核心计算:
    """
    核心计算类
    负责交易逻辑处理、策略执行等功能
    """
    
    def __init__(self, 参_数据库管理器=None, 参_UI更新器=None):
        """
        初始化核心计算类

        @param 参_数据库管理器: 数据库管理器实例，用于访问数据库
        @param 参_UI更新器: 线程安全UI更新器实例，用于跨线程更新UI
        @return None
        @exception Exception: 初始化过程中可能发生的未知错误
        """
        # 步骤1: 记录初始化信息
        # 记录核心计算类初始化的日志
        print("核心计算类初始化开始")
        logger.info("核心计算类初始化开始")

        # 步骤2: 保存数据库管理器引用
        # 如果外部传入了数据库管理器，则使用该实例
        # 否则在需要时再导入并创建
        self.类_数据库管理器 = 参_数据库管理器

        # 步骤3: 保存UI更新器引用
        # 保存线程安全UI更新器的引用，用于跨线程更新UI
        self.类_UI更新器 = 参_UI更新器

        # 步骤4: 初始化交易所API接口
        # 目前仅支持OKX接口，后续可以扩展
        self.类_OKX接口 = 类_OKX()

        # 步骤5: 记录初始化完成
        # 记录核心计算类初始化完成的日志
        print("核心计算类初始化完成")
        logger.info("核心计算类初始化完成")
    
    def 处理逻辑主函数(self, 参_机器人编号: str, 参_交易对: str, 参_交易所: str, 参_下单模式: str) -> Dict[str, Any]:
        """
        处理交易逻辑，根据交易所和下单模式执行不同的操作
        
        @param 参_机器人编号 (str): 机器人编号
        @param 参_交易对 (str): 交易对名称
        @param 参_交易所 (str): 交易所名称
        @param 参_下单模式 (str): 下单模式
        @return Dict[str, Any]: 处理结果
        @exception Exception: 处理过程中可能发生的未知错误
        """
        # 步骤1: 记录开始处理
        # 记录开始处理交易逻辑的信息
        print(f"开始处理交易逻辑: 机器人编号={参_机器人编号}, 交易对={参_交易对}, 交易所={参_交易所}, 下单模式={参_下单模式}")
        logger.info(f"开始处理交易逻辑: 机器人编号={参_机器人编号}, 交易对={参_交易对}, 交易所={参_交易所}, 下单模式={参_下单模式}")
        
        结果 = {
            "状态": "失败",
            "信息": "",
            "数据": {}
        }
        
        try:
            # 步骤2: 获取机器人策略信息
            # 从数据库中获取该机器人的策略配置
            机器人数据 = self.获取机器人策略信息(参_机器人编号)
            if not 机器人数据:
                结果["信息"] = f"未找到机器人数据: {参_机器人编号}"
                logger.warning(结果["信息"])
                return 结果
            
            # 步骤3: 根据交易所分发处理
            # 根据不同的交易所调用不同的处理方法
            if 参_交易所 == "OKX":
                return self.处理OKX逻辑(参_机器人编号, 参_交易对, 机器人数据, 参_下单模式)
            elif 参_交易所 == "币安":
                结果["信息"] = "币安交易所暂未实现"
                logger.warning(结果["信息"])
                return 结果
            else:
                结果["信息"] = f"不支持的交易所: {参_交易所}"
                logger.warning(结果["信息"])
                return 结果
                
        except Exception as e:
            # 步骤4: 异常处理
            # 如果处理过程中出现异常，记录错误日志
            结果["信息"] = f"处理交易逻辑出错: {e}"
            logger.error(结果["信息"], exc_info=True)
            print(结果["信息"])
            return 结果
    
    def 处理OKX逻辑(self, 参_机器人编号: str, 参_交易对: str, 参_机器人数据: Dict[str, Any], 参_下单模式: str) -> Dict[str, Any]:
        """
        处理OKX交易所的交易逻辑
        
        @param 参_机器人编号 (str): 机器人编号
        @param 参_交易对 (str): 交易对名称
        @param 参_机器人数据 (Dict[str, Any]): 机器人数据
        @param 参_下单模式 (str): 下单模式
        @return Dict[str, Any]: 处理结果
        @exception Exception: 处理过程中可能发生的未知错误
        """
        # 步骤1: 记录开始处理
        # 记录开始处理OKX交易逻辑的信息
        print(f"开始处理OKX交易逻辑: 机器人编号={参_机器人编号}, 交易对={参_交易对}")
        logger.info(f"开始处理OKX交易逻辑: 机器人编号={参_机器人编号}, 交易对={参_交易对}")
        
        结果 = {
            "状态": "失败",
            "信息": "",
            "数据": {}
        }
        
        try:
            # 步骤2: 根据下单模式处理
            # 根据不同的下单模式执行不同的操作
            if 参_下单模式 == "本地模拟":
                return self.OKX本地模拟(参_机器人编号, 参_交易对, 参_机器人数据)
           
            else:
                结果["信息"] = f"不支持的下单模式: {参_下单模式}"
                logger.warning(结果["信息"])
                return 结果
                
        except Exception as e:
            # 步骤3: 异常处理
            # 如果处理过程中出现异常，记录错误日志
            结果["信息"] = f"处理OKX交易逻辑出错: {e}"
            logger.error(结果["信息"], exc_info=True)
            print(结果["信息"])
            return 结果
    
    def OKX本地模拟(self, 参_机器人编号: str, 参_交易对: str, 参_机器人数据: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理OKX本地模拟的交易逻辑
        
        @param 参_机器人编号 (str): 机器人编号
        @param 参_交易对 (str): 交易对名称
        @param 参_机器人数据 (Dict[str, Any]): 机器人数据
        @return Dict[str, Any]: 处理结果
        @exception Exception: 处理过程中可能发生的未知错误
        """
        # 步骤1: 记录开始处理
        # 记录开始处理OKX本地模拟的信息
        print(f"开始处理OKX本地模拟: 机器人编号={参_机器人编号}, 交易对={参_交易对}")
        logger.info(f"开始处理OKX本地模拟: 机器人编号={参_机器人编号}, 交易对={参_交易对}")
        
        结果 = {
            "状态": "成功",
            "信息": "本地模拟处理完成",
            "数据": {
                "持仓信息": [],
                "订单信息": []
            }
        }
        
        try:
            # 步骤2: 获取策略参数
            # 根据机器人数据获取策略参数
            策略参数 = self.获取策略参数(参_机器人数据)
            if not 策略参数:
                结果["状态"] = "失败"
                结果["信息"] = "未找到有效的策略参数"
                return 结果
            
            # 步骤3: 获取持仓信息
            # 在本地模式下，从数据库中获取持仓信息
            持仓信息 = self.获取本地持仓信息(参_机器人编号, 参_交易对)
            结果["数据"]["持仓信息"] = 持仓信息
            
            # 步骤4: 获取K线数据
            # 使用OKX接口批量获取多个周期的K线数据
            所需周期列表 = 策略参数.get("所需周期", ["1m"])
            多周期K线数据 = self.类_OKX接口.批量获取蜡烛数据(参_交易对, 所需周期列表, 100)
            if not 多周期K线数据:
                结果["状态"] = "失败"
                结果["信息"] = "批量获取K线数据失败"
                return 结果
            # 获取最新一根蜡烛的收盘价作为最新价格
            最新价格 = 多周期K线数据[所需周期列表[-1]][-1]['收盘价']
            # 记录获取到的周期数据
            print(f"成功获取 {len(多周期K线数据)} 个周期的K线数据")
            logger.info(f"成功获取 {len(多周期K线数据)} 个周期的K线数据")
            for 周期, 数据列表 in 多周期K线数据.items():
                print(f"{周期} 周期: 获取到 {len(数据列表)} 条数据")
                logger.info(f"{周期} 周期: 获取到 {len(数据列表)} 条数据")
            
            # 步骤5: 判断机器人是否有持仓
            # 检查持仓信息中的持仓订单数是否大于0
            # 根据持仓状态执行不同的交易逻辑
            if isinstance(持仓信息, dict) and 持仓信息.get('持仓订单数', 0) > 0:
                # 有持仓
                # 这里可以添加有持仓时的处理逻辑
                print(f"机器人 {参_机器人编号} 有持仓，持仓订单数: {持仓信息.get('持仓订单数')}")
                logger.info(f"机器人 {参_机器人编号} 有持仓，持仓订单数: {持仓信息.get('持仓订单数')}")
                
            else:
                # 没有持仓
                # 这里可以添加无持仓时的处理逻辑
                开仓返回值 = self.本地模拟开仓(参_机器人编号, 参_交易对, 参_机器人数据, 最新价格, 多周期K线数据)

                print(f"机器人 {参_机器人编号} 没有持仓")
                logger.info(f"机器人 {参_机器人编号} 没有持仓")
                
         
            








            # 步骤6: 记录处理完成
            # 记录本地模式处理完成的信息
            print(f"OKX本地模拟处理完成: 机器人编号={参_机器人编号}, 交易对={参_交易对}")
            logger.info(f"OKX本地模拟处理完成: 机器人编号={参_机器人编号}, 交易对={参_交易对}")
            
            return 结果
            
        except Exception as e:
            # 步骤7: 异常处理
            # 如果处理过程中出现异常，记录错误日志
            结果["状态"] = "失败"
            结果["信息"] = f"处理OKX本地模拟出错: {e}"
            logger.error(结果["信息"], exc_info=True)
            print(结果["信息"])
            return 结果
    

    def 获取机器人策略信息(self, 参_机器人编号: str) -> Optional[Dict[str, Any]]:
        """
        从数据库中获取机器人的策略信息
        
        @param 参_机器人编号 (str): 机器人编号
        @return Optional[Dict[str, Any]]: 机器人数据，如果未找到则返回None
        @exception Exception: 获取过程中可能发生的未知错误
        """
        try:
            # 步骤1: 检查数据库管理器
            # 如果没有数据库管理器，则导入并创建
            if not self.类_数据库管理器:
                from 数据库.数据库管理 import 类_数据库管理
                self.类_数据库管理器 = 类_数据库管理()
            
            # 步骤2: 获取机器人数据
            # 从数据库中获取指定机器人编号的数据
            机器人数据 = self.类_数据库管理器.获取机器人(参_机器人编号)
            
            # 步骤3: 解析策略参数
            # 如果机器人数据中包含策略参数JSON，则解析为字典
            if 机器人数据 and '策略参数JSON' in 机器人数据:
                try:
                    机器人数据['策略参数'] = json.loads(机器人数据['策略参数JSON'])
                except:
                    机器人数据['策略参数'] = {}
            
            return 机器人数据
            
        except Exception as e:
            # 步骤4: 异常处理
            # 如果获取过程中出现异常，记录错误日志
            logger.error(f"获取机器人策略信息失败: {e}", exc_info=True)
            print(f"获取机器人策略信息失败: {e}")
            return None
    
    def 获取策略参数(self, 参_机器人数据: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        根据机器人数据获取策略参数
        
        @param 参_机器人数据 (Dict[str, Any]): 机器人数据
        @return Optional[Dict[str, Any]]: 策略参数，如果未找到则返回None
        @exception Exception: 获取过程中可能发生的未知错误
        """
        try:
            # 步骤1: 检查机器人数据
            # 如果机器人数据为空或不包含必要的策略信息，则返回None
            if not 参_机器人数据 or '策略类型' not in 参_机器人数据 or '子策略' not in 参_机器人数据:
                logger.warning("机器人数据中缺少策略信息")
                return None
            
            # 步骤2: 获取策略参数
            # 使用AI策略参数类获取对应的策略参数
            策略类型 = 参_机器人数据['策略类型']
            子策略 = 参_机器人数据['子策略']
            
            策略参数 = 类_AI策略参数.获取策略参数(策略类型, 子策略)
            
            # 步骤3: 检查策略参数
            # 如果没有找到对应的策略参数，则记录警告日志
            if not 策略参数:
                logger.warning(f"未找到策略参数: 策略类型={策略类型}, 子策略={子策略}")
            
            return 策略参数
            
        except Exception as e:
            # 步骤4: 异常处理
            # 如果获取过程中出现异常，记录错误日志
            logger.error(f"获取策略参数失败: {e}", exc_info=True)
            print(f"获取策略参数失败: {e}")
            return None
    
    def 获取本地持仓信息(self, 参_机器人编号: str, 参_交易对: str) -> Optional[Dict[str, Any]]:
        """
        获取本地持仓信息, 返回包含持仓列表、持仓订单数、持仓均价、持仓数量和开仓总金额的字典
        
        @param 参_机器人编号 (str): 机器人编号
        @param 参_交易对 (str): 交易对名称
        @return Optional[Dict[str, Any]]: 持仓信息，如果未找到则返回None
        @exception Exception: 获取过程中可能发生的未知错误
        """
        try:
            # 步骤1: 检查数据库管理器
            # 如果没有数据库管理器，则导入并创建
            if not self.类_数据库管理器:
                from 数据库.数据库管理 import 类_数据库管理
                self.类_数据库管理器 = 类_数据库管理()
            
            # 步骤2: 获取持仓信息

            持仓信息 = self.类_数据库管理器.获取持仓信息(参_机器人编号, 参_交易对)
            
            # 如果没有持仓数据，返回None
            if not 持仓信息:
                logger.info(f"未找到本地持仓信息: 机器人编号={参_机器人编号}, 交易对={参_交易对}")
                return None
           
            logger.info(f"获取本地持仓信息成功: 机器人编号={参_机器人编号}, 交易对={参_交易对}, 持仓数量={持仓信息['持仓数量']}")
            # 步骤3: 返回完整持仓信息
            # 返回包含持仓列表、持仓订单数、持仓均价、持仓数量和开仓总金额的字典
            return {
                '持仓列表': 持仓信息['持仓列表'],
                '持仓订单数': 持仓信息['持仓订单数'],
                '持仓均价': 持仓信息['持仓均价'],
                '持仓数量': 持仓信息['持仓数量'],
                '开仓总金额': 持仓信息['开仓总金额']
            }
            
        except Exception as e:
            # 步骤3: 异常处理
            # 如果获取过程中出现异常，记录错误日志
            logger.error(f"获取本地持仓信息失败: {e}", exc_info=True)
            print(f"获取本地持仓信息失败: {e}")
            return None
    def 本地模拟开仓(self, 参_机器人编号: str, 参_交易对: str, 参_机器人数据: Dict[str, Any], 参_最新价格: float, 参_蜡烛数据: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        本地模拟开仓, 根据机器人数据和交易对数据，模拟开仓
        
        @param 参_机器人编号 (str): 机器人编号
        @param 参_交易对 (str): 交易对名称
        @param 参_机器人数据 (Dict[str, Any]): 机器人数据
        @param 参_最新价格 (float): 最新价格
        @param 参_蜡烛数据 (Dict[str, Any]): 蜡烛数据
        @return Optional[Dict[str, Any]]: 开仓结果，如果未找到则返回None
        @exception Exception: 处理过程中可能发生的未知错误
        """
        # 步骤1: 记录开始处理
        # 记录开始本地模拟开仓的信息
        print(f"开始本地模拟开仓: 机器人编号={参_机器人编号}, 交易对={参_交易对}, 最新价格={参_最新价格}")
        logger.info(f"开始本地模拟开仓: 机器人编号={参_机器人编号}, 交易对={参_交易对}, 最新价格={参_最新价格}")
        
        try:
            # 步骤2: 检查数据库管理器
            # 如果没有数据库管理器，则导入并创建
            if not self.类_数据库管理器:
                from 数据库.数据库管理 import 类_数据库管理
                self.类_数据库管理器 = 类_数据库管理()
            
            # 步骤3: 获取策略参数
            # 从机器人数据中获取策略参数
            策略参数 = self.获取策略参数(参_机器人数据)
            if not 策略参数:
                logger.warning(f"未找到有效的策略参数: 机器人编号={参_机器人编号}")
                return None
            
            # 步骤4: 计算开仓数量和金额
            # 根据机器人数据和策略参数计算开仓数量和金额
            # 获取首单金额，如果没有则使用默认值100
            首单金额 = float(参_机器人数据.get('预配首单', 0))
            # 如果首单金额=0,则返回None
            if 首单金额 == 0:
                logger.warning(f"首单金额为0: 机器人编号={参_机器人编号}, 交易对={参_交易对}")
                return None
            # 计算开仓数量 = 首单金额 / 最新价格
            开仓数量 = 首单金额 / float(参_最新价格)
            
            # 计算手续费率，默认为0.1%
            手续费率 = 0.001
            
            # 计算开仓手续费
            开仓手续费 = 首单金额 * 手续费率
            
            # 步骤5: 获取最新蜡烛时间
            # 从蜡烛数据中获取最新的蜡烛时间
            # 获取第一个周期的最后一根蜡烛
            周期列表 = list(参_蜡烛数据.keys())
            if not 周期列表:
                logger.warning(f"蜡烛数据为空: 机器人编号={参_机器人编号}, 交易对={参_交易对}")
                return None
                
            首个周期 = 周期列表[0]
            最新蜡烛 = 参_蜡烛数据[首个周期][-1]
            开仓蜡烛时间 = 最新蜡烛.get('时间戳', 0)
            
            # 步骤6: 构建持仓数据
            # 构建要添加到持仓表的数据字典
            import time
            当前时间戳 = int(time.time() * 1000)  # 毫秒时间戳
            
            持仓数据 = {
                "交易对": 参_交易对,
                "开仓蜡烛时间": 开仓蜡烛时间,
                "开仓时间": 当前时间戳,
                "开仓金额": 首单金额,
                "开仓数量": 开仓数量,
                "开仓手续费": 开仓手续费,
                "策略配置": 参_机器人数据.get('策略配置', '自定义'),
                "策略类型": 参_机器人数据.get('策略类型', '通用型'),
                "子策略": 参_机器人数据.get('子策略', '默认策略'),
                "机器人编号": 参_机器人编号
            }
            
            # 步骤7: 添加持仓记录
            # 调用数据库管理器的添加持仓记录方法
            添加结果 = self.类_数据库管理器.添加持仓记录(持仓数据)
            
            if not 添加结果:
                logger.warning(f"添加持仓记录失败: 机器人编号={参_机器人编号}, 交易对={参_交易对}")
                return None
            
            # 步骤8: 更新中控数据
            # 更新机器人的下单金额、累计下单金额、订单数量、持仓均价和持仓数量
            try:
                # 步骤1: 获取最新的持仓信息
                # 从数据库中获取该机器人的最新持仓信息
                最新持仓信息 = self.类_数据库管理器.获取持仓信息(参_机器人编号, 参_交易对)
                
                if 最新持仓信息:
                    # 步骤2: 构建中控数据更新字典
                    # 准备需要更新到中控表格的数据
                    中控更新数据 = {
                        "机器人编号": 参_机器人编号,
                        "本次下单": 首单金额,  # 最新一单的开仓金额
                        "累计下单": 最新持仓信息['开仓总金额'],  # 所有开仓金额的和
                        "订单数量": 最新持仓信息['持仓订单数'],  # 持仓表订单数
                        "持仓均价": 最新持仓信息['持仓均价'],  # 更新后的持仓均价
                        "持仓数量": 最新持仓信息['持仓数量']  # 更新后的持仓数量
                    }
                    
                    # 步骤3: 更新数据库中的中控数据
                    # 调用数据库管理器更新机器人中控数据
                    机器人编号 = 中控更新数据["机器人编号"]  # 先保存值
                    中控更新结果 = self.类_数据库管理器.更新机器人中控数据(中控更新数据)
                    中控更新数据["机器人编号"] = 机器人编号  # 再恢复键值对
                    
                    if 中控更新结果:
                        # 步骤4: 通过线程安全UI更新器更新界面
                        # 使用线程安全的方式更新UI，避免跨线程问题
                        if self.类_UI更新器:
                            # 构建需要更新的字段字典
                            更新字段 = {
                                "本次下单": 中控更新数据["本次下单"],
                                "累计下单": 中控更新数据["累计下单"],
                                "订单数量": 中控更新数据["订单数量"],
                                "持仓均价": 中控更新数据["持仓均价"],
                                "持仓数量": 中控更新数据["持仓数量"]
                            }
                            # 发送批量更新请求
                            self.类_UI更新器.请求更新多个单元格(参_机器人编号, 更新字段)
                            logger.info(f"中控数据更新成功: 机器人编号={参_机器人编号}, 交易对={参_交易对}")
                        else:
                            logger.warning(f"UI更新器不可用，无法更新界面: 机器人编号={参_机器人编号}")

                    else:
                        logger.warning(f"中控数据更新失败: 机器人编号={参_机器人编号}, 交易对={参_交易对}")
            except Exception as e:
                logger.error(f"更新中控数据时出错: {e}", exc_info=True)
                print(f"更新中控数据时出错: {e}")
            
            # 步骤9: 构建返回结果
            # 构建包含开仓结果的字典
            开仓结果 = {
                "状态": "成功",
                "信息": "本地模拟开仓成功",
                "数据": 持仓数据
            }
            
            # 步骤10: 记录成功日志
            # 记录本地模拟开仓成功的日志
            logger.info(f"本地模拟开仓成功: 机器人编号={参_机器人编号}, 交易对={参_交易对}, 开仓金额={首单金额}, 开仓数量={开仓数量}")
            print(f"本地模拟开仓成功: 机器人编号={参_机器人编号}, 交易对={参_交易对}, 开仓金额={首单金额}, 开仓数量={开仓数量}")
            
            return 开仓结果
            
        except Exception as e:
            # 步骤10: 异常处理
            # 如果处理过程中出现异常，记录错误日志
            错误信息 = f"本地模拟开仓失败: {e}"
            logger.error(错误信息, exc_info=True)
            print(错误信息)
            return None