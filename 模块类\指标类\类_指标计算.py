class 类_指标计算:
    """
    技术指标计算类
    负责计算各种技术分析指标
    """

    def __init__(self):
        """
        初始化指标计算类
        """
        print("指标计算类初始化完成")

    def 计算CCI指标(self, 参_蜡烛数据列表, 参_周期=14):
        """
        计算CCI指标 (Commodity Channel Index)

        参数:
            参_蜡烛数据列表: 蜡烛数据列表，每个元素是字典格式，包含：
                {
                    "时间戳": "1640995200000",
                    "开盘价": "46200.1",
                    "最高价": "46200.1", 
                    "最低价": "46100.1",
                    "收盘价": "46100.1",
                    "成交量": "0.1",
                    "成交额": "4610.01"
                }
            参_周期: 计算周期，默认14天

        返回:
            CCI指标值列表
        """
        try:
            # 检查数据长度是否足够
            if len(参_蜡烛数据列表) < 参_周期:
                print(f"错误：数据长度不足，需要至少{参_周期}个数据点")
                return []

            # 从蜡烛数据字典中提取价格信息
            最高价列表 = []
            最低价列表 = []
            收盘价列表 = []

            for 蜡烛数据 in 参_蜡烛数据列表:
                if isinstance(蜡烛数据, dict) and all(key in 蜡烛数据 for key in ["最高价", "最低价", "收盘价"]):
                    # 处理字典格式的蜡烛数据
                    最高价列表.append(float(蜡烛数据["最高价"]))
                    最低价列表.append(float(蜡烛数据["最低价"]))
                    收盘价列表.append(float(蜡烛数据["收盘价"]))
                elif isinstance(蜡烛数据, list) and len(蜡烛数据) >= 4:
                    # 兼容列表格式的蜡烛数据
                    最高价列表.append(float(蜡烛数据[1]))  # 最高价
                    最低价列表.append(float(蜡烛数据[2]))  # 最低价
                    收盘价列表.append(float(蜡烛数据[3]))  # 收盘价
                else:
                    print("错误：蜡烛数据格式不正确")
                    return []

            CCI列表 = []

            # 计算每个时间点的CCI值
            # 对于OKX数据，索引0是最新数据，我们需要从索引0开始计算
            for i in range(0, len(收盘价列表) - 参_周期 + 1):
                # 步骤1: MA计算 - 取最近N根K线的收盘价，把这N个数值全部相加，再除以N
                # 对于OKX数据，从索引i开始的N根K线
                开始位置 = i
                结束位置 = i + 参_周期
                最近N根收盘价 = 收盘价列表[开始位置:结束位置]
                MA = sum(最近N根收盘价) / len(最近N根收盘价)

                # 步骤2: MD计算 - 对这N根K线中的每一根，先算(MA-该根收盘价)，取绝对值；把得到的N个绝对值全部相加，再除以N
                MD总和 = 0
                for 收盘价 in 最近N根收盘价:
                    MD总和 += abs(MA - 收盘价)
                MD = MD总和 / len(最近N根收盘价)

                # 步骤3: TP计算 - 在当前这一根K线，把最高价、最低价、收盘价三个数值相加，再除以3
                当前最高价 = 最高价列表[i]
                当前最低价 = 最低价列表[i]
                当前收盘价 = 收盘价列表[i]
                TP = (当前最高价 + 当前最低价 + 当前收盘价) / 3

                # 步骤4: CCI计算 - 用当前这一根K线的收盘价，减去第1步求出的MA，再除以(第2步求出的MD×0.015)
                if MD == 0:
                    CCI = 0  # 避免除零错误
                else:
                    CCI = (当前收盘价 - MA) / (MD * 0.015)

                CCI列表.append(CCI)

            print(f"CCI指标计算完成，共计算{len(CCI列表)}个数据点")
            return CCI列表

        except Exception as e:
            print(f"计算CCI指标时发生错误: {e}")
            return []

    def 计算ATR指标(self, 参_蜡烛数据列表, 参_周期=14):
        """
        计算ATR指标 (Average True Range)
        ATR用于衡量市场波动性，是真实波幅的平均值
        使用RMA（Relative Moving Average）平滑方法计算
        计算顺序：从最旧数据开始计算，最后颠倒列表，使0号索引为最新ATR

        参数:
            参_蜡烛数据列表: 蜡烛数据列表，每个元素是字典格式，包含：
                {
                    "时间戳": "1640995200000",
                    "开盘价": "46200.1",
                    "最高价": "46200.1", 
                    "最低价": "46100.1",
                    "收盘价": "46100.1",
                    "成交量": "0.1",
                    "成交额": "4610.01"
                }
            参_周期: 计算周期，默认14天

        返回:
            ATR指标值列表（使用RMA平滑），0号索引为最新ATR
        """
        try:
            # 检查数据长度是否足够
            if len(参_蜡烛数据列表) < 参_周期 + 1:
                print(f"错误：数据长度不足，需要至少{参_周期 + 1}个数据点")
                return []

            # 从蜡烛数据字典中提取价格信息
            最高价列表 = []
            最低价列表 = []
            收盘价列表 = []

            for 蜡烛数据 in 参_蜡烛数据列表:
                if isinstance(蜡烛数据, dict) and all(key in 蜡烛数据 for key in ["最高价", "最低价", "收盘价"]):
                    # 处理字典格式的蜡烛数据
                    最高价列表.append(float(蜡烛数据["最高价"]))
                    最低价列表.append(float(蜡烛数据["最低价"]))
                    收盘价列表.append(float(蜡烛数据["收盘价"]))
                elif isinstance(蜡烛数据, list) and len(蜡烛数据) >= 4:
                    # 兼容列表格式的蜡烛数据
                    最高价列表.append(float(蜡烛数据[1]))  # 最高价
                    最低价列表.append(float(蜡烛数据[2]))  # 最低价
                    收盘价列表.append(float(蜡烛数据[3]))  # 收盘价
                else:
                    print("错误：蜡烛数据格式不正确")
                    return []

            # 颠倒价格列表，让最旧的数据在前面（索引0）
            最高价列表.reverse()
            最低价列表.reverse()
            收盘价列表.reverse()

            # 计算真实波幅(True Range)
            TR列表 = []
            
            # 从索引1开始计算（因为需要前一根K线的收盘价）
            for i in range(1, len(最高价列表)):
                当前最高价 = 最高价列表[i]
                当前最低价 = 最低价列表[i]
                前一根收盘价 = 收盘价列表[i-1]
                
                # 计算三个差值
                差值1 = 当前最高价 - 当前最低价  # 当前K线的高低点差
                差值2 = abs(当前最高价 - 前一根收盘价)  # 当前最高价与前一根收盘价的差
                差值3 = abs(当前最低价 - 前一根收盘价)  # 当前最低价与前一根收盘价的差
                
                # 取三个差值中的最大值作为真实波幅
                TR = max(差值1, 差值2, 差值3)
                TR列表.append(TR)

            # 计算ATR（使用RMA平滑）
            ATR列表 = []
            
            # 第一个ATR值是前N个TR的简单平均
            if len(TR列表) >= 参_周期:
                第一个ATR = sum(TR列表[:参_周期]) / 参_周期
                ATR列表.append(第一个ATR)
                
                # 后续的ATR值使用RMA平滑公式：(前一个ATR * (N-1) + 当前TR) / N
                # 这就是RMA（Relative Moving Average）平滑方法
                for i in range(参_周期, len(TR列表)):
                    前一个ATR = ATR列表[-1]
                    当前TR = TR列表[i]
                    新ATR = (前一个ATR * (参_周期 - 1) + 当前TR) / 参_周期
                    ATR列表.append(新ATR)

            # 颠倒ATR列表，让最新的ATR在索引0位置
            ATR列表.reverse()

            print(f"ATR指标计算完成，共计算{len(ATR列表)}个数据点，0号索引为最新ATR")
            return ATR列表

        except Exception as e:
            print(f"计算ATR指标时发生错误: {e}")
            return []
