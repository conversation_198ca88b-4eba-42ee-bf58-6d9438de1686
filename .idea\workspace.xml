<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="1c531075-4f33-4672-8eeb-8348d6fd70d5" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/模块类/功能类/类_核心计算.py" beforeDir="false" afterPath="$PROJECT_DIR$/模块类/功能类/类_核心计算.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/窗口UI布局/UI管理/策略窗口模块/类_策略编辑窗口.py" beforeDir="false" afterPath="$PROJECT_DIR$/窗口UI布局/UI管理/策略窗口模块/类_策略编辑窗口.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2zrwF5AiKCnMOTbXnuvZ4v8CcCH" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "Python.main.executor": "Debug",
    "Python.类_OKX接口.executor": "Debug",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "master",
    "junie.onboarding.icon.badge.shown": "true",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-PY-251.26927.74" />
        <option value="bundled-python-sdk-657d8234b839-64d779b69b7a-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.26927.74" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="1c531075-4f33-4672-8eeb-8348d6fd70d5" name="更改" comment="" />
      <created>1752502393416</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752502393416</updated>
      <workItem from="1752502394376" duration="52000" />
      <workItem from="1752687213439" duration="67000" />
      <workItem from="1752758519865" duration="252000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/Quantification$_OKX.coverage" NAME="类_OKX接口 覆盖结果" MODIFIED="1752687247027" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/模块类/交易所API接口/OKX" />
    <SUITE FILE_PATH="coverage/Quantification$main.coverage" NAME="main 覆盖结果" MODIFIED="1752758633130" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
  </component>
</project>