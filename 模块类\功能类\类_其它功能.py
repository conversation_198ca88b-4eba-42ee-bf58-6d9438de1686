# -*- coding: utf-8 -*-
"""
其它功能
包含获取系统配置等通用功能
"""
import logging
from typing import Dict, Any, Optional, Tuple
import os
import sys

# 获取当前模块的日志记录器
logger = logging.getLogger(__name__)

# 添加项目根目录到系统路径
当前路径 = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if 当前路径 not in sys.path:
    sys.path.append(当前路径)

# 导入数据库管理类
from 数据库.数据库管理 import 类_数据库管理

class 类_其它功能:
    """
    其它功能类
    提供获取系统配置等通用功能
    """
    
    def __init__(self):
        """
        初始化其它功能类
        
        @return None
        @exception Exception: 初始化过程中可能发生的未知错误
        """
        # 步骤1: 初始化数据库管理类
        # 创建数据库管理实例，用于访问数据库
        self.数据库管理 = 类_数据库管理()
        
        # 步骤2: 记录初始化完成
        # 记录其它功能类初始化完成的信息
        logger.info("其它功能类初始化完成")
    
    def 获取系统配置(self, 参_配置键: str) -> Optional[str]:
        """
        获取系统配置
        
        @param 参_配置键 (str): 配置键名
        @return Optional[str]: 配置值，如果不存在则返回None
        @exception Exception: 获取配置过程中可能发生的未知错误
        """
        try:
            # 步骤1: 从数据库获取配置
            # 调用数据库管理类的获取系统配置方法
            配置值 = self.数据库管理.获取系统配置(参_配置键)
            
            # 步骤2: 记录获取结果
            # 记录获取到的配置值
            if 配置值 is not None:
                logger.info(f"获取系统配置成功: {参_配置键} = {配置值}")
            else:
                logger.warning(f"系统配置不存在: {参_配置键}")
            
            # 步骤3: 返回配置值
            # 返回从数据库获取到的配置值
            return 配置值
            
        except Exception as e:
            # 步骤4: 异常处理
            # 如果获取过程中出现异常，记录错误日志并返回None
            logger.error(f"获取系统配置出错: {e}", exc_info=True)
            return None
    
    def 获取交易所和下单模式(self) -> Tuple[str, str]:
        """
        获取系统配置中的交易所和下单模式
        
        @return Tuple[str, str]: 交易所名称和下单模式的元组
        @exception Exception: 获取配置过程中可能发生的未知错误
        """
        try:
            # 步骤1: 获取交易所配置
            # 从系统配置中获取当前使用的交易所
            交易所 = self.获取系统配置("当前交易所")
            
            # 步骤2: 获取下单模式配置
            # 从系统配置中获取当前使用的下单模式
            下单模式 = self.获取系统配置("下单模式")
            
            # 步骤3: 设置默认值
            # 如果配置不存在，设置默认值
            if 交易所 is None:
                交易所 = "币安"  # 默认使用币安
                logger.warning(f"交易所配置不存在，使用默认值: {交易所}")
            
            if 下单模式 is None:
                下单模式 = "测试模式"  # 默认使用测试模式
                logger.warning(f"下单模式配置不存在，使用默认值: {下单模式}")
            
            # 步骤4: 记录获取结果
            # 记录获取到的交易所和下单模式
            logger.info(f"当前交易所: {交易所}, 下单模式: {下单模式}")
            
            # 步骤5: 返回结果
            # 返回交易所和下单模式的元组
            return 交易所, 下单模式
            
        except Exception as e:
            # 步骤6: 异常处理
            # 如果获取过程中出现异常，记录错误日志并返回默认值
            logger.error(f"获取交易所和下单模式出错: {e}", exc_info=True)
            return "币安", "测试模式"  # 出错时返回默认值


# 测试代码
if __name__ == "__main__":
    # 设置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 创建其它功能类实例
    其它功能 = 类_其它功能()
    
    # 测试获取系统配置
    print("测试获取系统配置...")
    测试配置 = 其它功能.获取系统配置("测试配置")
    print(f"测试配置: {测试配置}")
    
    # 测试获取交易所和下单模式
    print("测试获取交易所和下单模式...")
    交易所, 下单模式 = 其它功能.获取交易所和下单模式()
    print(f"当前交易所: {交易所}, 下单模式: {下单模式}")
    
   





