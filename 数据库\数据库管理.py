# -*- coding: utf-8 -*-
"""
数据库管理类
负责SQLite数据库的创建、连接和数据操作
"""

import logging
from typing import List, Dict, Any, Optional
from .数据访问 import (
    类_数据库连接管理,
    类_表结构管理,
    类_机器人数据访问,
    类_配置数据访问,
    类_交易对数据访问,
    类_开平仓日志数据访问,
    类_持仓数据访问  # 注意：需要先创建类_持仓数据访问.py文件，再取消注释
)

# 获取当前模块的日志记录器
logger = logging.getLogger(__name__)

class 类_数据库管理:
    """
    数据库管理类
    负责处理所有数据库相关操作
    """
    
    def __init__(self, 参_数据库路径: Optional[str] = None):
        """
        初始化数据库管理类
        
        参数:
            参_数据库路径: 数据库文件路径，如果为None则使用默认路径
        """
        self.类_模块名 = "数据库管理"
        
        # 初始化数据库连接管理
        self.类_数据库连接管理 = 类_数据库连接管理(参_数据库路径)
        
        # 初始化表结构管理
        self.类_表结构管理 = 类_表结构管理(self.类_数据库连接管理)
        
        # 初始化各个数据访问类
        self.类_机器人数据访问 = 类_机器人数据访问(self.类_数据库连接管理)
        self.类_配置数据访问 = 类_配置数据访问(self.类_数据库连接管理)
        self.类_交易对数据访问 = 类_交易对数据访问(self.类_数据库连接管理)
        self.类_开平仓日志数据访问 = 类_开平仓日志数据访问(self.类_数据库连接管理)
        self.类_持仓数据访问 = 类_持仓数据访问(self.类_数据库连接管理)
        # 初始化默认交易对数据,如果数据库中没有交易对数据，则添加默认的交易对
        self.类_交易对数据访问.初始化默认交易对数据()
        
        logger.info(f"数据库管理类初始化完成，数据库路径: {self.类_数据库连接管理.类_数据库路径}")
        print(f"数据库管理类初始化完成，数据库路径: {self.类_数据库连接管理.类_数据库路径}")
    
    def 关闭连接(self):
        """
        关闭数据库连接
        """
        self.类_数据库连接管理.关闭连接()
    
    # ========== 机器人相关方法 ==========
    
    def 添加机器人(self, 参_机器人数据: Dict[str, Any]) -> bool:
        """
        添加机器人到中控表
        
        参数:
            参_机器人数据: 机器人数据字典
            
        返回:
            添加是否成功
        """
        return self.类_机器人数据访问.添加机器人(参_机器人数据)
    
    def 获取所有机器人(self) -> List[Dict[str, Any]]:
        """
        获取所有机器人数据
        
        返回:
            机器人数据列表
        """
        return self.类_机器人数据访问.获取所有机器人()
    
    def 获取机器人(self, 参_机器人编号: str) -> Optional[Dict[str, Any]]:
        """
        获取单个机器人数据
        
        参数:
            参_机器人编号: 机器人编号
            
        返回:
            机器人数据字典，如果未找到返回None
        """
        return self.类_机器人数据访问.获取机器人(参_机器人编号)
    
    def 更新机器人(self, 参_机器人编号: str, 参_更新数据: Dict[str, Any]) -> bool:
        """
        更新机器人数据
        
        参数:
            参_机器人编号: 机器人编号
            参_更新数据: 要更新的数据字典
            
        返回:
            更新是否成功
        """
        return self.类_机器人数据访问.更新机器人(参_机器人编号, 参_更新数据)
    
    def 更新机器人状态(self, 参_机器人编号: str, 参_状态数据: Dict[str, Any]) -> bool:
        """
        更新机器人状态
        
        参数:
            参_机器人编号: 机器人编号
            参_状态数据: 要更新的状态数据
            
        返回:
            更新是否成功
        """
        return self.类_机器人数据访问.更新机器人状态(参_机器人编号, 参_状态数据)
        
    def 更新机器人中控数据(self, 参_中控数据: Dict[str, Any]) -> bool:
        """
        更新机器人中控数据，包括本次下单、累计下单、订单数量、持仓均价和持仓数量
        
        参数:
            参_中控数据: 要更新的中控数据字典，必须包含机器人编号
            
        返回:
            更新是否成功
        """
        return self.类_机器人数据访问.更新机器人中控数据(参_中控数据)
        
    def 删除机器人(self, 参_机器人编号: str) -> bool:
        """
        删除机器人
        
        参数:
            参_机器人编号: 机器人编号
            
        返回:
            删除是否成功
        """
        return self.类_机器人数据访问.删除机器人(参_机器人编号)
    
    # ========== API配置相关方法 ==========
    
    def 保存API配置(self, 参_交易所名称: str, 参_api_key: str, 参_secret_key: str, 参_passphrase: Optional[str] = None) -> bool:
        """
        保存API配置
        
        参数:
            参_交易所名称: 交易所名称
            参_api_key: API Key
            参_secret_key: Secret Key
            参_passphrase: Passphrase (OKX需要)
            
        返回:
            保存是否成功
        """
        return self.类_配置数据访问.保存API配置(参_交易所名称, 参_api_key, 参_secret_key, 参_passphrase)
    
    def 获取API配置(self, 参_交易所名称: str) -> Optional[Dict[str, Any]]:
        """
        获取API配置
        
        参数:
            参_交易所名称: 交易所名称
            
        返回:
            API配置信息字典，如果不存在则返回None
        """
        return self.类_配置数据访问.获取API配置(参_交易所名称)
    
    # ========== 系统配置相关方法 ==========
    
    def 保存系统配置(self, 参_配置键: str, 参_配置值: str) -> bool:
        """
        保存系统配置
        
        参数:
            参_配置键: 配置键
            参_配置值: 配置值
            
        返回:
            保存是否成功
        """
        return self.类_配置数据访问.保存系统配置(参_配置键, 参_配置值)
    
    def 获取系统配置(self, 参_配置键: str) -> Optional[str]:
        """
        获取系统配置
        
        参数:
            参_配置键: 配置键
            
        返回:
            配置值，如果不存在则返回None
        """
        return self.类_配置数据访问.获取系统配置(参_配置键)
    
    # ========== 交易对配置相关方法 ==========
    
    def 添加交易对配置(self, 参_交易对数据: Dict[str, Any]) -> bool:
        """
        添加交易对配置
        
        参数:
            参_交易对数据: 交易对配置数据字典
            
        返回:
            操作是否成功
        """
        return self.类_交易对数据访问.添加交易对配置(参_交易对数据)
    
    def 获取交易对列表(self, 参_交易所: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        获取交易对列表
        
        参数:
            参_交易所: 交易所名称，如果为None则获取所有交易所的交易对
            
        返回:
            交易对配置列表
        """
        return self.类_交易对数据访问.获取交易对列表(参_交易所)
    
    def 初始化默认交易对数据(self):
        """
        初始化默认交易对数据
        为币安和OKX交易所添加BTC、ETH、SOL三个交易对
        """
        self.类_交易对数据访问.初始化默认交易对数据()
    
    # ========== 开平仓日志相关方法 ==========
    
    def 添加开平仓日志(self, 参_日志数据: Dict[str, Any]) -> bool:
        """
        添加开平仓日志记录
        
        参数:
            参_日志数据: 开平仓日志数据字典
            
        返回:
            添加是否成功
        """
        return self.类_开平仓日志数据访问.添加开平仓日志(参_日志数据)
    
    def 获取开平仓日志列表(self) -> List[Dict[str, Any]]:
        """
        获取开平仓日志列表
        
        参数:
            参_限制数量: 限制返回的记录数量
            
        返回:
            开平仓日志记录列表
        """
        return self.类_开平仓日志数据访问.获取开平仓日志列表()
    
    # ========== 持仓相关方法 ==========
    # 注意：需要先创建类_持仓数据访问.py文件，再实现下面的方法
    
    def 添加持仓记录(self, 参_持仓数据: Dict[str, Any]) -> bool:
        """
        添加持仓记录
        
        参数:
            参_持仓数据: 持仓数据字典
            
        返回:
            添加是否成功
        """
        return self.类_持仓数据访问.添加持仓记录(参_持仓数据)
    
    def 获取持仓记录(self, 参_机器人编号: str, 参_交易对: str) -> List[Dict[str, Any]]:
        """
        获取指定机器人和交易对的持仓记录
        
        参数:
            参_机器人编号: 机器人编号
            参_交易对: 交易对名称
            
        返回:
            持仓记录列表
        """
        return self.类_持仓数据访问.获取持仓记录(参_机器人编号, 参_交易对)
    
    def 获取持仓信息(self, 参_机器人编号: str, 参_交易对: str) -> Optional[Dict[str, Any]]:
        """
        获取持仓信息，包括持仓记录列表、持仓均价、持仓数量等
        
        参数:
            参_机器人编号: 机器人编号
            参_交易对: 交易对名称
            
        返回:
            持仓信息，如果未找到则返回None
        """
        return self.类_持仓数据访问.获取持仓信息(参_机器人编号, 参_交易对)
    
    def 删除持仓记录(self, 参_序号: int) -> bool:
        """
        删除指定序号的持仓记录
        
        参数:
            参_序号: 持仓记录序号
            
        返回:
            删除是否成功
        """
        return self.类_持仓数据访问.删除持仓记录(参_序号)
    
    def 清空持仓记录(self, 参_机器人编号: str, 参_交易对: Optional[str] = None) -> bool:
        """
        清空指定机器人的持仓记录，可选择指定交易对
        
        参数:
            参_机器人编号: 机器人编号
            参_交易对: 交易对名称，如果为None则清空所有交易对的持仓记录
            
        返回:
            清空是否成功
        """
        return self.类_持仓数据访问.清空持仓记录(参_机器人编号, 参_交易对)
    
    # ========== 表管理相关方法 ==========
    
    def 清空表(self, 参_表名: str) -> bool:
        """
        清空指定的数据表
        
        参数:
            参_表名: 要清空的表名，可以是 "中控"、"持仓"、"平仓"、"日志" 等
            
        返回:
            清空是否成功
        """
        return self.类_表结构管理.清空表(参_表名)
    
    def 清空所有表(self) -> bool:
        """
        清空所有数据表
        
        返回:
            清空是否成功
        """
        try:
            # 清空中控表
            self.清空表("中控表")
            
            # 清空开平仓日志表
            self.清空表("开平仓日志表")
            
            # 清空持仓表（如果有）
            self.清空表("持仓表")
            
            logger.info("所有数据表已清空")
            return True
        except Exception as e:
            logger.error(f"清空所有表失败: {e}", exc_info=True)
            return False
    
    def __del__(self):
        """
        析构函数，确保关闭数据库连接
        """
        try:
            self.关闭连接()
        except:
            pass


# 测试代码
if __name__ == "__main__":
    """
    测试数据库管理类
    """
    # 创建数据库管理实例
    数据库管理 = 类_数据库管理()
    
    # 测试保存API配置
    print("测试保存API配置...")
    数据库管理.保存API配置("币安", "test_api_key", "test_secret_key")
    数据库管理.保存API配置("OKX", "test_api_key", "test_secret_key", "test_passphrase")
    
    # 测试获取API配置
    print("测试获取API配置...")
    币安配置 = 数据库管理.获取API配置("币安")
    print(f"币安配置: {币安配置}")
    
    # 测试系统配置
    print("测试系统配置...")
    数据库管理.保存系统配置("测试配置", "测试值")
    测试配置值 = 数据库管理.获取系统配置("测试配置")
    print(f"测试配置值: {测试配置值}")
    
    # 测试添加机器人
    print("测试添加机器人...")
    机器人数据 = {
        "交易对": "BTC/USDT",
        "预配本金": 1000.0,
        "预配首单": 100.0,
        "机器人编号": "BOT001"
    }
    数据库管理.添加机器人(机器人数据)
    
    # 测试获取所有机器人
    print("测试获取所有机器人...")
    机器人列表 = 数据库管理.获取所有机器人()
    print(f"机器人列表: {机器人列表}")
    
    # 测试添加日志
    print("测试添加日志...")
    日志数据 = {
        "交易对": "BTC/USDT",
        "机器人编号": "BOT001",
        "订单类型": "开仓",
        "金额": 100.0,
        "数量": 0.001
    }
    数据库管理.添加开平仓日志(日志数据)
    
    # 测试获取日志列表
    print("测试获取日志列表...")
    日志列表 = 数据库管理.获取开平仓日志列表()
    print(f"日志列表: {日志列表}")
    
    print("数据库管理类测试完成") 